<?php

namespace Tests\Feature\Http\Controllers\Estate\Estate;

use App\Models\CheckedBy;
use App\Models\CreatedBy;
use App\Models\Department;
use App\Models\ExternalMaintenance;
use App\Models\ModifiedBy;
use App\Models\WorkType;
use App\Models\Workflow;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Carbon;
use JMac\Testing\Traits\AdditionalAssertions;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * @see \App\Http\Controllers\Estate\Estate\ExternalMaintenanceController
 */
final class ExternalMaintenanceControllerTest extends TestCase
{
    use AdditionalAssertions, RefreshDatabase, WithFaker;

    #[Test]
    public function index_behaves_as_expected(): void
    {
        $externalMaintenances = ExternalMaintenance::factory()->count(3)->create();

        $response = $this->get(route('external-maintenances.index'));

        $response->assertOk();
        $response->assertJsonStructure([]);
    }


    #[Test]
    public function store_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\Estate\Estate\ExternalMaintenanceController::class,
            'store',
            \App\Http\Requests\Estate\Estate\ExternalMaintenanceStoreRequest::class
        );
    }

    #[Test]
    public function store_saves(): void
    {
        $type = fake()->randomElement(/** enum_attributes **/);
        $entry_date = Carbon::parse(fake()->dateTime());
        $requesting_agent_id = fake()->word();
        $department = Department::factory()->create();
        $location = fake()->text();
        $work_type = WorkType::factory()->create();
        $other_work_type = fake()->word();
        $description = fake()->text();
        $proposed_action = fake()->text();
        $cost_implication = fake()->randomFloat(/** decimal_attributes **/);
        $checked_by = CheckedBy::factory()->create();
        $supporting_doc_url = fake()->text();
        $notify_by_email = fake()->boolean();
        $notify_by_phone = fake()->boolean();
        $notify_in_app = fake()->boolean();
        $workflow = Workflow::factory()->create();
        $status = fake()->randomElement(/** enum_attributes **/);
        $created_by = CreatedBy::factory()->create();
        $modified_by = ModifiedBy::factory()->create();

        $response = $this->post(route('external-maintenances.store'), [
            'type' => $type,
            'entry_date' => $entry_date->toDateTimeString(),
            'requesting_agent_id' => $requesting_agent_id,
            'department_id' => $department->id,
            'location' => $location,
            'work_type_id' => $work_type->id,
            'other_work_type' => $other_work_type,
            'description' => $description,
            'proposed_action' => $proposed_action,
            'cost_implication' => $cost_implication,
            'checked_by' => $checked_by->id,
            'supporting_doc_url' => $supporting_doc_url,
            'notify_by_email' => $notify_by_email,
            'notify_by_phone' => $notify_by_phone,
            'notify_in_app' => $notify_in_app,
            'workflow_id' => $workflow->id,
            'status' => $status,
            'created_by' => $created_by->id,
            'modified_by' => $modified_by->id,
        ]);

        $externalMaintenances = ExternalMaintenance::query()
            ->where('type', $type)
            ->where('entry_date', $entry_date)
            ->where('requesting_agent_id', $requesting_agent_id)
            ->where('department_id', $department->id)
            ->where('location', $location)
            ->where('work_type_id', $work_type->id)
            ->where('other_work_type', $other_work_type)
            ->where('description', $description)
            ->where('proposed_action', $proposed_action)
            ->where('cost_implication', $cost_implication)
            ->where('checked_by', $checked_by->id)
            ->where('supporting_doc_url', $supporting_doc_url)
            ->where('notify_by_email', $notify_by_email)
            ->where('notify_by_phone', $notify_by_phone)
            ->where('notify_in_app', $notify_in_app)
            ->where('workflow_id', $workflow->id)
            ->where('status', $status)
            ->where('created_by', $created_by->id)
            ->where('modified_by', $modified_by->id)
            ->get();
        $this->assertCount(1, $externalMaintenances);
        $externalMaintenance = $externalMaintenances->first();

        $response->assertCreated();
        $response->assertJsonStructure([]);
    }


    #[Test]
    public function show_behaves_as_expected(): void
    {
        $externalMaintenance = ExternalMaintenance::factory()->create();

        $response = $this->get(route('external-maintenances.show', $externalMaintenance));

        $response->assertOk();
        $response->assertJsonStructure([]);
    }


    #[Test]
    public function update_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\Estate\Estate\ExternalMaintenanceController::class,
            'update',
            \App\Http\Requests\Estate\Estate\ExternalMaintenanceUpdateRequest::class
        );
    }

    #[Test]
    public function update_behaves_as_expected(): void
    {
        $externalMaintenance = ExternalMaintenance::factory()->create();
        $type = fake()->randomElement(/** enum_attributes **/);
        $entry_date = Carbon::parse(fake()->dateTime());
        $requesting_agent_id = fake()->word();
        $department = Department::factory()->create();
        $location = fake()->text();
        $work_type = WorkType::factory()->create();
        $other_work_type = fake()->word();
        $description = fake()->text();
        $proposed_action = fake()->text();
        $cost_implication = fake()->randomFloat(/** decimal_attributes **/);
        $checked_by = CheckedBy::factory()->create();
        $supporting_doc_url = fake()->text();
        $notify_by_email = fake()->boolean();
        $notify_by_phone = fake()->boolean();
        $notify_in_app = fake()->boolean();
        $workflow = Workflow::factory()->create();
        $status = fake()->randomElement(/** enum_attributes **/);
        $created_by = CreatedBy::factory()->create();
        $modified_by = ModifiedBy::factory()->create();

        $response = $this->put(route('external-maintenances.update', $externalMaintenance), [
            'type' => $type,
            'entry_date' => $entry_date->toDateTimeString(),
            'requesting_agent_id' => $requesting_agent_id,
            'department_id' => $department->id,
            'location' => $location,
            'work_type_id' => $work_type->id,
            'other_work_type' => $other_work_type,
            'description' => $description,
            'proposed_action' => $proposed_action,
            'cost_implication' => $cost_implication,
            'checked_by' => $checked_by->id,
            'supporting_doc_url' => $supporting_doc_url,
            'notify_by_email' => $notify_by_email,
            'notify_by_phone' => $notify_by_phone,
            'notify_in_app' => $notify_in_app,
            'workflow_id' => $workflow->id,
            'status' => $status,
            'created_by' => $created_by->id,
            'modified_by' => $modified_by->id,
        ]);

        $externalMaintenance->refresh();

        $response->assertOk();
        $response->assertJsonStructure([]);

        $this->assertEquals($type, $externalMaintenance->type);
        $this->assertEquals($entry_date, $externalMaintenance->entry_date);
        $this->assertEquals($requesting_agent_id, $externalMaintenance->requesting_agent_id);
        $this->assertEquals($department->id, $externalMaintenance->department_id);
        $this->assertEquals($location, $externalMaintenance->location);
        $this->assertEquals($work_type->id, $externalMaintenance->work_type_id);
        $this->assertEquals($other_work_type, $externalMaintenance->other_work_type);
        $this->assertEquals($description, $externalMaintenance->description);
        $this->assertEquals($proposed_action, $externalMaintenance->proposed_action);
        $this->assertEquals($cost_implication, $externalMaintenance->cost_implication);
        $this->assertEquals($checked_by->id, $externalMaintenance->checked_by);
        $this->assertEquals($supporting_doc_url, $externalMaintenance->supporting_doc_url);
        $this->assertEquals($notify_by_email, $externalMaintenance->notify_by_email);
        $this->assertEquals($notify_by_phone, $externalMaintenance->notify_by_phone);
        $this->assertEquals($notify_in_app, $externalMaintenance->notify_in_app);
        $this->assertEquals($workflow->id, $externalMaintenance->workflow_id);
        $this->assertEquals($status, $externalMaintenance->status);
        $this->assertEquals($created_by->id, $externalMaintenance->created_by);
        $this->assertEquals($modified_by->id, $externalMaintenance->modified_by);
    }


    #[Test]
    public function destroy_deletes_and_responds_with(): void
    {
        $externalMaintenance = ExternalMaintenance::factory()->create();

        $response = $this->delete(route('external-maintenances.destroy', $externalMaintenance));

        $response->assertNoContent();

        $this->assertModelMissing($externalMaintenance);
    }
}

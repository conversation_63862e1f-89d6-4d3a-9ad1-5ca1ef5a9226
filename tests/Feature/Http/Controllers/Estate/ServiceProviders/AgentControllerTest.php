<?php

namespace Tests\Feature\Http\Controllers\Estate\ServiceProviders;

use App\Models\Agent;
use App\Models\CreatedBy;
use App\Models\ModifiedBy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Carbon;
use JMac\Testing\Traits\AdditionalAssertions;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

/**
 * @see \App\Http\Controllers\Estate\ServiceProviders\AgentController
 */
final class AgentControllerTest extends TestCase
{
    use AdditionalAssertions, RefreshDatabase, WithFaker;

    #[Test]
    public function index_behaves_as_expected(): void
    {
        $agents = Agent::factory()->count(3)->create();

        $response = $this->get(route('agents.index'));

        $response->assertOk();
        $response->assertJsonStructure([]);
    }


    #[Test]
    public function store_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\Estate\ServiceProviders\AgentController::class,
            'store',
            \App\Http\Requests\Estate\ServiceProviders\AgentStoreRequest::class
        );
    }

    #[Test]
    public function store_saves(): void
    {
        $cooperate_name = fake()->word();
        $cooperate_address = fake()->text();
        $cooperate_telephone = fake()->word();
        $cooperate_email = fake()->word();
        $cooperate_registration_no = fake()->word();
        $cooperate_nature_of_business = fake()->word();
        $company_logo = fake()->word();
        $fullname = fake()->word();
        $gender = fake()->randomElement(/** enum_attributes **/);
        $religion = fake()->word();
        $marital_status = fake()->randomElement(/** enum_attributes **/);
        $personal_email = fake()->word();
        $residential_address = fake()->word();
        $city_id = fake()->numberBetween(-10000, 10000);
        $means_of_identification = fake()->randomElement(/** enum_attributes **/);
        $date_of_birth = Carbon::parse(fake()->date());
        $mobile_number = fake()->word();
        $tenant_category_id = fake()->numberBetween(-10000, 10000);
        $created_by = CreatedBy::factory()->create();
        $modified_by = ModifiedBy::factory()->create();

        $response = $this->post(route('agents.store'), [
            'cooperate_name' => $cooperate_name,
            'cooperate_address' => $cooperate_address,
            'cooperate_telephone' => $cooperate_telephone,
            'cooperate_email' => $cooperate_email,
            'cooperate_registration_no' => $cooperate_registration_no,
            'cooperate_nature_of_business' => $cooperate_nature_of_business,
            'company_logo' => $company_logo,
            'fullname' => $fullname,
            'gender' => $gender,
            'religion' => $religion,
            'marital_status' => $marital_status,
            'personal_email' => $personal_email,
            'residential_address' => $residential_address,
            'city_id' => $city_id,
            'means_of_identification' => $means_of_identification,
            'date_of_birth' => $date_of_birth->toDateString(),
            'mobile_number' => $mobile_number,
            'tenant_category_id' => $tenant_category_id,
            'created_by' => $created_by->id,
            'modified_by' => $modified_by->id,
        ]);

        $agents = Agent::query()
            ->where('cooperate_name', $cooperate_name)
            ->where('cooperate_address', $cooperate_address)
            ->where('cooperate_telephone', $cooperate_telephone)
            ->where('cooperate_email', $cooperate_email)
            ->where('cooperate_registration_no', $cooperate_registration_no)
            ->where('cooperate_nature_of_business', $cooperate_nature_of_business)
            ->where('company_logo', $company_logo)
            ->where('fullname', $fullname)
            ->where('gender', $gender)
            ->where('religion', $religion)
            ->where('marital_status', $marital_status)
            ->where('personal_email', $personal_email)
            ->where('residential_address', $residential_address)
            ->where('city_id', $city_id)
            ->where('means_of_identification', $means_of_identification)
            ->where('date_of_birth', $date_of_birth)
            ->where('mobile_number', $mobile_number)
            ->where('tenant_category_id', $tenant_category_id)
            ->where('created_by', $created_by->id)
            ->where('modified_by', $modified_by->id)
            ->get();
        $this->assertCount(1, $agents);
        $agent = $agents->first();

        $response->assertCreated();
        $response->assertJsonStructure([]);
    }


    #[Test]
    public function show_behaves_as_expected(): void
    {
        $agent = Agent::factory()->create();

        $response = $this->get(route('agents.show', $agent));

        $response->assertOk();
        $response->assertJsonStructure([]);
    }


    #[Test]
    public function update_uses_form_request_validation(): void
    {
        $this->assertActionUsesFormRequest(
            \App\Http\Controllers\Estate\ServiceProviders\AgentController::class,
            'update',
            \App\Http\Requests\Estate\ServiceProviders\AgentUpdateRequest::class
        );
    }

    #[Test]
    public function update_behaves_as_expected(): void
    {
        $agent = Agent::factory()->create();
        $cooperate_name = fake()->word();
        $cooperate_address = fake()->text();
        $cooperate_telephone = fake()->word();
        $cooperate_email = fake()->word();
        $cooperate_registration_no = fake()->word();
        $cooperate_nature_of_business = fake()->word();
        $company_logo = fake()->word();
        $fullname = fake()->word();
        $gender = fake()->randomElement(/** enum_attributes **/);
        $religion = fake()->word();
        $marital_status = fake()->randomElement(/** enum_attributes **/);
        $personal_email = fake()->word();
        $residential_address = fake()->word();
        $city_id = fake()->numberBetween(-10000, 10000);
        $means_of_identification = fake()->randomElement(/** enum_attributes **/);
        $date_of_birth = Carbon::parse(fake()->date());
        $mobile_number = fake()->word();
        $tenant_category_id = fake()->numberBetween(-10000, 10000);
        $created_by = CreatedBy::factory()->create();
        $modified_by = ModifiedBy::factory()->create();

        $response = $this->put(route('agents.update', $agent), [
            'cooperate_name' => $cooperate_name,
            'cooperate_address' => $cooperate_address,
            'cooperate_telephone' => $cooperate_telephone,
            'cooperate_email' => $cooperate_email,
            'cooperate_registration_no' => $cooperate_registration_no,
            'cooperate_nature_of_business' => $cooperate_nature_of_business,
            'company_logo' => $company_logo,
            'fullname' => $fullname,
            'gender' => $gender,
            'religion' => $religion,
            'marital_status' => $marital_status,
            'personal_email' => $personal_email,
            'residential_address' => $residential_address,
            'city_id' => $city_id,
            'means_of_identification' => $means_of_identification,
            'date_of_birth' => $date_of_birth->toDateString(),
            'mobile_number' => $mobile_number,
            'tenant_category_id' => $tenant_category_id,
            'created_by' => $created_by->id,
            'modified_by' => $modified_by->id,
        ]);

        $agent->refresh();

        $response->assertOk();
        $response->assertJsonStructure([]);

        $this->assertEquals($cooperate_name, $agent->cooperate_name);
        $this->assertEquals($cooperate_address, $agent->cooperate_address);
        $this->assertEquals($cooperate_telephone, $agent->cooperate_telephone);
        $this->assertEquals($cooperate_email, $agent->cooperate_email);
        $this->assertEquals($cooperate_registration_no, $agent->cooperate_registration_no);
        $this->assertEquals($cooperate_nature_of_business, $agent->cooperate_nature_of_business);
        $this->assertEquals($company_logo, $agent->company_logo);
        $this->assertEquals($fullname, $agent->fullname);
        $this->assertEquals($gender, $agent->gender);
        $this->assertEquals($religion, $agent->religion);
        $this->assertEquals($marital_status, $agent->marital_status);
        $this->assertEquals($personal_email, $agent->personal_email);
        $this->assertEquals($residential_address, $agent->residential_address);
        $this->assertEquals($city_id, $agent->city_id);
        $this->assertEquals($means_of_identification, $agent->means_of_identification);
        $this->assertEquals($date_of_birth, $agent->date_of_birth);
        $this->assertEquals($mobile_number, $agent->mobile_number);
        $this->assertEquals($tenant_category_id, $agent->tenant_category_id);
        $this->assertEquals($created_by->id, $agent->created_by);
        $this->assertEquals($modified_by->id, $agent->modified_by);
    }


    #[Test]
    public function destroy_deletes_and_responds_with(): void
    {
        $agent = Agent::factory()->create();

        $response = $this->delete(route('agents.destroy', $agent));

        $response->assertNoContent();

        $this->assertModelMissing($agent);
    }
}

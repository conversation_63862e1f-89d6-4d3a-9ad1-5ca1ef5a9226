<?php

namespace Database\Factories\Estate\ServiceProviders;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Estate\ServiceProviders\Agent;
use App\Models\User;

class AgentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Agent::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'cooperate_name' => fake()->word(),
            'cooperate_address' => fake()->text(),
            'cooperate_telephone' => fake()->word(),
            'cooperate_email' => fake()->word(),
            'cooperate_registration_no' => fake()->word(),
            'cooperate_nature_of_business' => fake()->word(),
            'company_logo' => fake()->word(),
            'fullname' => fake()->word(),
            'gender' => fake()->randomElement(["male","female"]),
            'religion' => fake()->word(),
            'marital_status' => fake()->randomElement(["single","married","divorced","widowed"]),
            'personal_email' => fake()->word(),
            'residential_address' => fake()->word(),
            'city_id' => fake()->numberBetween(-10000, 10000),
            'means_of_identification' => fake()->randomElement(["national_id","passport","driver_license"]),
            'date_of_birth' => fake()->date(),
            'mobile_number' => fake()->word(),
            'tenant_category_id' => fake()->numberBetween(-10000, 10000),
            'created_by' => User::factory(),
            'modified_by' => User::factory(),
        ];
    }
}

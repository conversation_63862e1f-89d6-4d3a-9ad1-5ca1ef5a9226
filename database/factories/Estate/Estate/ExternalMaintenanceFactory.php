<?php

namespace Database\Factories\Estate\Estate;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Estate\Estate\Department;
use App\Models\Estate\Estate\ExternalMaintenance;
use App\Models\Estate\Estate\WorkType;
use App\Models\Shared\Workflow;
use App\Models\User;

class ExternalMaintenanceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ExternalMaintenance::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'type' => fake()->randomElement(["internal","external"]),
            'entry_date' => fake()->dateTime(),
            'requesting_agent_id' => fake()->word(),
            'department_id' => Department::factory(),
            'location' => fake()->text(),
            'work_type_id' => WorkType::factory(),
            'other_work_type' => fake()->word(),
            'description' => fake()->text(),
            'proposed_action' => fake()->text(),
            'cost_implication' => fake()->randomFloat(0, 0, 9999999999.),
            'checked_by' => User::factory(),
            'supporting_doc_url' => fake()->text(),
            'notify_by_email' => fake()->boolean(),
            'notify_by_phone' => fake()->boolean(),
            'notify_in_app' => fake()->boolean(),
            'workflow_id' => Workflow::factory(),
            'status' => fake()->randomElement(["completed","refunded","failed"]),
            'created_by' => User::factory(),
            'modified_by' => User::factory(),
        ];
    }
}

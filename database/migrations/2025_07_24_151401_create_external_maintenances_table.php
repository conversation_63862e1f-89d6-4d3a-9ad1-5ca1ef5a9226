<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('external_maintenances', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ["internal","external"])->index();
            $table->dateTime('entry_date');
            $table->string('requesting_agent_id');
            $table->foreignId('department_id')->constrained();
            $table->longText('location');
            $table->foreignId('work_type_id')->constrained();
            $table->string('other_work_type');
            $table->longText('description');
            $table->longText('proposed_action');
            $table->decimal('cost_implication');
            $table->foreignId('checked_by')->constrained('users');
            $table->longText('supporting_doc_url');
            $table->boolean('notify_by_email')->default(false);
            $table->boolean('notify_by_phone')->default(false);
            $table->boolean('notify_in_app')->default(false);
            $table->foreignId('workflow_id')->constrained();
            $table->enum('status', ["completed","refunded","failed"]);
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('modified_by')->constrained('users');
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('external_maintenances');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('agents', function (Blueprint $table) {
            $table->id();
            $table->string('cooperate_name');
            $table->longText('cooperate_address');
            $table->string('cooperate_telephone');
            $table->string('cooperate_email');
            $table->string('cooperate_registration_no');
            $table->string('cooperate_nature_of_business');
            $table->string('company_logo');
            $table->string('fullname');
            $table->enum('gender', ["male","female"]);
            $table->string('religion');
            $table->enum('marital_status', ["single","married","divorced","widowed"]);
            $table->string('personal_email');
            $table->string('residential_address');
            $table->integer('city_id');
            $table->enum('means_of_identification', ["national_id","passport","driver_license"]);
            $table->date('date_of_birth');
            $table->string('mobile_number');
            $table->integer('tenant_category_id');
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('modified_by')->constrained('users');
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agents');
    }
};

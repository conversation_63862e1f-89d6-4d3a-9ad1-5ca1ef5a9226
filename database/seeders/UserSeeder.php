<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $is_in_production = config('settings.app.env') === 'production';
        $default_password = 'password';

        $accounts = [
            [
                '001',
                '<PERSON>',
                '<EMAIL>',
                '***********',
                bcrypt($is_in_production ? 'iadasuma' : $default_password),
            ],
            [
                '002',
                '<PERSON><PERSON><PERSON>',
                '<EMAIL>',
                '***********',
                bcrypt($is_in_production ? 'suledeeman' : $default_password),
            ],
            [
                '003',
                '<PERSON>aru<PERSON>',
                '<EMAIL>',
                '***********',
                bcrypt($is_in_production ? 'aburuqayyah' : $default_password),
            ],
            [
                '004',
                '<PERSON>',
                '<EMAIL>',
                '***********',
                bcrypt($is_in_production ? 'gmb12345678' : $default_password),
            ],
            [
                '005',
                '<PERSON>',
                '<EMAIL>',
                '***********',
                bcrypt($is_in_production ? 'maigoro1@password' : $default_password),
            ],
        ];

        foreach ($accounts as $account) {
            User::updateOrCreate(
                ['identifier' => $account[0]],
                [
                    'fullname' => $account[1],
                    'email' => $account[2],
                    'phone_no' => $account[3],
                    'profile_picture' => null,
                    'is_active' => true,
                    'email_verified_at' => now(),
                    'password' => $account[4],
                    'rank_id' => null,
                    'first_access' => null,
                    'last_access' => null,
                    'remember_token' => null,
                ]
            );
        }
    }
}

<?php

namespace Database\Seeders;

use App\Models\Core\Country;
use App\Models\Core\State;
use App\Models\Core\City;
use App\Models\Core\Lga;
use App\Models\Core\Timezone;
use App\Models\Core\Currency;
use App\Models\Core\Language;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Builder as SchemaBuilder;

class CoreSeeder extends Seeder
{
    protected SchemaBuilder $schema;
    private array $countries = [
        'data' => [],
    ];

    private array $modules = [
        'states' => [
            'class' => State::class,
            'data' => [],
            'enabled' => true,
        ],
        'cities' => [
            'class' => City::class,
            'data' => [],
            'enabled' => true,
        ],
        'lgas' => [
            'class' => Lga::class,
            'data' => [],
            'enabled' => true,
        ],
        'timezones' => [
            'class' => Timezone::class,
            'enabled' => true,
        ],
        'currencies' => [
            'class' => Currency::class,
            'data' => [],
            'enabled' => true,
        ],
        'languages' => [
            'class' => Language::class,
            'data' => [],
            'enabled' => true,
        ],
    ];

    public function __construct()
    {
        $this->schema = Schema::connection(env('DB_CONNECTION'));

        // countries
        $this->initCountries();
        // init modules
        foreach ($this->modules as $module => $options) {
            if ($options['enabled'] === true) {
                $this->initModule($module);
            }
        }
    }

    public function run(): void
    {
        $this->command->getOutput()->block('Seeding start');

        $this->command->getOutput()->progressStart(count($this->countries['data']));

        // country schema
        $countryFields = $this->schema->getColumnListing('countries');

        $this->forgetFields($countryFields, ['id']);

        foreach (array_chunk($this->countries['data'], 20) as $countryChunks) {
            foreach ($countryChunks as $countryArray) {
                $countryArray = array_map(fn($field) => gettype($field) === 'string' ? trim($field) : $field, $countryArray);

                $country = Country::create(Arr::only($countryArray, $countryFields));
                // states, cities and lgas
                if ($this->isModuleEnabled('states')) {
                    $this->seedStates($country, $countryArray);
                }
                // timezones
                if ($this->isModuleEnabled('timezones')) {
                    $this->seedTimezones($country, $countryArray);
                }
                // currencies
                if ($this->isModuleEnabled('currencies')) {
                    $this->seedCurrencies($country, $countryArray);
                }

                $this->command->getOutput()->progressAdvance();
            }
        }

        // languages
        if ($this->isModuleEnabled('languages')) {
            $this->seedLanguages();
        }

        $this->command->getOutput()->progressFinish();

        $this->command->getOutput()->block('Seeding end');
    }

    /**
     * @param string $module
     * @return void
     */
    private function initModule(string $module)
    {
        if (array_key_exists($module, $this->modules)) {
            // truncate module database table.
            $this->schema->disableForeignKeyConstraints();
            app($this->modules[$module]['class'])->truncate();
            $this->schema->enableForeignKeyConstraints();
            // import json data.
            $moduleSourcePath = __DIR__ . '/../../resources/json/' . $module . '.json';

            if (File::exists($moduleSourcePath)) {
                $this->modules[$module]['data'] = json_decode(File::get($moduleSourcePath), true);
            }
        }
    }

    /**
     * @param string $module
     * @return bool
     */
    private function isModuleEnabled(string $module): bool
    {
        return $this->modules[$module]['enabled'];
    }

    /**
     * @return void
     */
    private function initCountries(): void
    {
        $this->schema->disableForeignKeyConstraints();
        app(Country::class)->truncate();
        $this->schema->enableForeignKeyConstraints();

        $this->countries['data'] = json_decode(File::get(__DIR__ . '/../../resources/json/countries.json'), true);
    }

    /**
     * @param Country $country
     * @param array $countryArray
     *
     * @throws Exception
     */
    private function seedStates(Country $country, array $countryArray): void
    {
        // country states, cities and lga's for Nigeria
        $countryStates = Arr::where(
            $this->modules['states']['data'],
            fn($state) => $state['country_id'] === $countryArray['id']
        );
        // state schema
        $stateFields = $this->schema->getColumnListing('states');

        $this->forgetFields($stateFields, ['id', 'country_id']);

        $bulk_states = [];

        foreach ($countryStates as $stateArray) {
            $stateArray = array_map(fn($field) => gettype($field) === 'string' ? trim($field) : $field, $stateArray);

            $bulk_states[] = Arr::add(
                Arr::only($stateArray, $stateFields),
                'country_id',
                $country->id
            );
        }

        DB::beginTransaction();

        try {
            $last_state_id_before_insert = $this->findLastStateIdBeforeInsert();

            State::query()->insert($bulk_states);

            $bulk_states = $this->addStateIdAfterInsert($bulk_states, $last_state_id_before_insert);

            //state cities
            if ($this->isModuleEnabled('cities')) {
                $stateNames = array_column($bulk_states, 'name');

                $stateCities = Arr::where(
                    $this->modules['cities']['data'],
                    fn($city) => $city['country_id'] === $countryArray['id']
                        && in_array($city['state_name'], $stateNames, true)
                );

                $this->seedCities($country, $bulk_states, $stateCities);
            }

            if ($this->isModuleEnabled('lgas')) {
                $stateIds = array_column($bulk_states, 'id');

                $stateLgas = Arr::where(
                    $this->modules['lgas']['data'],
                    fn($lga) => $lga['country_id'] === $country->id
                        && in_array($lga['state_id'], $stateIds, true)
                );

                $this->seedLgas($country, $bulk_states, $stateLgas);
            }
        } catch (Exception $exception) {
            throw $exception;
        } finally {
            DB::commit();
        }
    }

    /**
     * @param Country $country
     * @param array $states
     * @param array $cities
     */
    private function seedCities(Country $country, array $states, array $cities): void
    {
        // city schema
        $cityFields = $this->schema->getColumnListing('cities');

        $this->forgetFields($cityFields, ['id', 'country_id', 'state_id']);

        //using array_chunk to prevent mySQL too many placeholders error
        foreach (array_chunk($cities, 500) as $cityChunks) {
            $cities_bulk = [];
            foreach ($cityChunks as $cityArray) {
                $cityArray = array_map(fn($field) => gettype($field) === 'string' ? trim($field) : $field, $cityArray);

                $city = Arr::only($cityArray, $cityFields);

                $state = Arr::first($states, fn($state) => $state['name'] === $cityArray['state_name']);

                $city = Arr::add(
                    $city,
                    'state_id',
                    $state['id']
                );

                $city = Arr::add(
                    $city,
                    'country_id',
                    $country->id
                );

                $cities_bulk[] = $city;
            }

            City::query()->insert($cities_bulk);
        }
    }

    /**
     * @param Country $country
     * @param array $states
     * @param array $cities
     */
    private function seedLgas(Country $country, array $states, array $lgas): void
    {
        // lga schema
        $lgaFields = $this->schema->getColumnListing('lgas');

        $this->forgetFields($lgaFields, ['id', 'country_id', 'state_id']);

        //using array_chunk to prevent mySQL too many placeholders error
        foreach (array_chunk($lgas, 500) as $lgaChunks) {
            $lga_bulk = [];
            foreach ($lgaChunks as $lgaArray) {
                $lgaArray = array_map(fn($field) => gettype($field) === 'string' ? trim($field) : $field, $lgaArray);

                $lga = Arr::only($lgaArray, $lgaFields);

                $state = Arr::first($states, fn($state) => $state['id'] === $lgaArray['state_id']);

                $lga = Arr::add(
                    $lga,
                    'state_id',
                    $state['id']
                );
                $lga = Arr::add(
                    $lga,
                    'state_code',
                    $state['state_code']
                );

                $lga = Arr::add(
                    $lga,
                    'country_id',
                    $country->id
                );
                $lga = Arr::add(
                    $lga,
                    'country_code',
                    $country->iso2
                );

                $lga_bulk[] = $lga;
            }

            Lga::query()->insert($lga_bulk);
        }
    }

    /**
     * @param Country $country
     * @param $countryArray
     * @return void
     */
    private function seedTimezones(Country $country, $countryArray): void
    {
        $bulk_timezones = [];

        foreach ($countryArray['timezones'] as $timezone) {
            $bulk_timezones[] = [
                'country_id' => $country->id,
                'name' => (string) $timezone['zoneName']
            ];
        }

        Timezone::query()->insert($bulk_timezones);
    }

    private function seedCurrencies(Country $country, array $countryArray): void
    {
        // currencies
        $exists = in_array($countryArray['currency'], array_keys($this->modules['currencies']['data']), true);
        $currency = $exists
            ? $this->modules['currencies']['data'][$countryArray['currency']]
            : [
                'name' => (string) $countryArray['currency'],
                'code' => (string) $countryArray['currency'],
                'symbol' => (string) $countryArray['currency_symbol'],
                'symbol_native' => (string) $countryArray['currency_symbol'],
                'decimal_digits' => 2,
            ];
        $country
            ->currency()
            ->create([
                'name' => (string) $currency['name'],
                'code' => (string) $currency['code'],
                'symbol' => (string) $currency['symbol'],
                'symbol_native' => (string) $currency['symbol_native'],
                'precision' => (int) $currency['decimal_digits'],
            ]);
    }

    /**
     * @return void
     */
    private function seedLanguages(): void
    {
        // languages
        Language::query()->insert($this->modules['languages']['data']);
    }

    /**
     * @param array $array
     * @param array $values
     * @return void
     */
    private function forgetFields(array &$array, array $values)
    {
        foreach ($values as $value) {
            if (($key = array_search($value, $array)) !== false) {
                unset($array[$key]);
            }
        }
    }

    private function findLastStateIdBeforeInsert()
    {
        $state = State::query()->orderByDesc('id')->first();

        $last_state_id_before_insert = 0;

        if (!is_null($state)) {
            $last_state_id_before_insert = $state->id;
        }

        return $last_state_id_before_insert;
    }

    private function addStateIdAfterInsert(array $bulk_states, $last_state_id_before_insert)
    {
        $count = count($bulk_states);

        for ($i = 1; $i <= $count; $i++) {
            $bulk_states[$i - 1]['id'] = $last_state_id_before_insert + $i;
        }
        return $bulk_states;
    }
}

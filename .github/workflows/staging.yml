name: 🚀 Deploy to Staging

on:
  push:
    branches: [ staging ]
  # pull_request:
  #   branches: [ staging ]
  #   types: [ closed ]

env:
  PHP_VERSION: '8.2'
  NODE_VERSION: '22'

jobs:
  deploy-staging:
    # Only run if PR was merged (not just closed)
    if: github.event.pull_request.merged == true || github.event_name == 'push'
    runs-on: ubuntu-latest
    environment: Staging

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐘 Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ env.PHP_VERSION }}
          tools: composer:v2

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 🔧 Install Dependencies
        run: |
          npm install
          npm run build
          composer config http-basic.composer.fluxui.dev "${{ secrets.FLUX_USERNAME }}" "${{ secrets.FLUX_LICENSE_KEY }}"
          composer install --no-interaction --prefer-dist --optimize-autoloader

      # - name: ⚙️ Create staging .env and .htaccess files
      #   run: |
      #     # 📄 Create .env file only if it doesn't exist on server
      #     if [ ! -f .env ]; then
      #       cp .env.example .env
      #       php artisan key:generate
      #       # 🔧 Set staging environment variables
      #       sed -i 's/APP_ENV=local/APP_ENV=staging/' .env
      #       sed -i 's|APP_URL=.*|APP_URL=https://staging.ims.npflpims.com|' .env
      #       # 🗄️ Set database credentials from secrets
      #       sed -i 's/DB_HOST=.*/DB_HOST=${{ secrets.DB_HOST }}/' .env
      #       sed -i 's/DB_PORT=.*/DB_PORT=${{ secrets.DB_PORT }}/' .env
      #       sed -i 's/DB_DATABASE=.*/DB_DATABASE=${{ secrets.DB_DATABASE }}/' .env
      #       sed -i 's/DB_USERNAME=.*/DB_USERNAME=${{ secrets.DB_USERNAME }}/' .env
      #       sed -i 's/DB_PASSWORD=.*/DB_PASSWORD=${{ secrets.DB_PASSWORD }}/' .env
      #       echo "✅ Created new .env file for staging with database credentials"
      #     else
      #       echo "ℹ️ .env file already exists on server, preserving existing configuration"
      #     fi

      #     # 📝 Create .htaccess file if it doesn't exist
      #     if [ ! -f public/.htaccess ]; then
      #       cat > public/.htaccess << 'EOF'
      #     <IfModule mod_rewrite.c>
      #         <IfModule mod_negotiation.c>
      #             Options -MultiViews -Indexes
      #         </IfModule>

      #         RewriteEngine On

      #         # Handle Authorization Header
      #         RewriteCond %{HTTP:Authorization} .
      #         RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

      #         # Redirect Trailing Slashes If Not A Folder...
      #         RewriteCond %{REQUEST_FILENAME} !-d
      #         RewriteCond %{REQUEST_URI} (.+)/$
      #         RewriteRule ^ %1 [L,R=301]

      #         # Send Requests To Front Controller...
      #         RewriteCond %{REQUEST_FILENAME} !-d
      #         RewriteCond %{REQUEST_FILENAME} !-f
      #         RewriteRule ^ index.php [L]
      #     </IfModule>
      #     EOF
      #     fi

      #     npm run build

      - name: 🚀 Deploy to Staging (if staging branch)
        if: github.ref == 'refs/heads/staging'
        uses: SamKirkland/FTP-Deploy-Action@v4.3.4
        with:
          server: ims.npflpims.com
          username: ${{ secrets.CPANEL_USERNAME }}
          password: ${{ secrets.CPANEL_PASSWORD }}
          local-dir: ./
          server-dir: /public_html/staging/
          exclude: |
            .git/**
            .github/**
            node_modules/**
            tests/**
            .env
            .env.example
            README.md
            phpunit.xml
            storage/logs/laravel.log
            storage/logs/worker.log

      - name: 🔧 Run post-deployment commands
        if: github.ref == 'refs/heads/staging'
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ims.npflpims.com
          username: ${{ secrets.CPANEL_USERNAME }}
          password: ${{ secrets.CPANEL_PASSWORD }}
          script: |
            cd /home/<USER>/public_html/staging

            # 📁 Configure subdomain document root to point to public folder
            # 📝 Create .htaccess in staging root to redirect to public
            # cat > .htaccess << 'EOF'
            # RewriteEngine On
            # RewriteCond %{REQUEST_URI} !^/public/
            # RewriteRule ^(.*)$ /public/$1 [L]
            # EOF

            # 🔗 Create storage link if it doesn't exist
            if [ ! -L public/storage ]; then
              php artisan storage:link
            fi

            # 🧹 Clear existing caches first
            php artisan config:clear
            php artisan route:clear
            php artisan view:clear
            php artisan cache:clear

            # ⚡ Cache optimization
            php artisan config:cache
            php artisan route:cache

            # 🎨 Try view cache (skip if it fails due to missing components)
            php artisan view:cache || echo "⚠️ View cache failed, skipping..."

            php artisan queue:restart
            cd ..
            php artisan queue:restart

            # 🗄️ Database operations (only if database is configured)
            # if php artisan migrate:status >/dev/null 2>&1; then
            #   echo "✅ Database connection successful, running migrations and seeders"
            #   php artisan migrate --force
            #   php artisan db:seed --force
            # else
            #   echo "⚠️ Database not configured or connection failed, skipping database operations"
            # fi

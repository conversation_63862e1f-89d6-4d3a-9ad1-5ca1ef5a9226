@props([
    'picture' => auth()->user()->profile_picture ?? 'default.jpg',
])
@php
    $breadCrumbs = [
        ['name' => __('Home'), 'url' => route('dashboard')],
        ['name' => __('My Profile'), 'url' => route('my-profile')],
    ]
@endphp
<x-app-layout :title="__('My Profile')" :breadCrumbs="$breadCrumbs">

    <!--begin::details Attention Message Area View-->
    <div class="card mb-5 mb-xl-10" id="kt_profile_details_view">
        <!--begin::Notice-->
        <div class="notice d-flex bg-light-warning rounded border-warning border border-dashed p-6">
            <!--begin::Icon-->
            <i class="ki-outline ki-information fs-2tx text-warning me-4"></i>
            <!--end::Icon-->
            <!--begin::Wrapper-->
            <div class="d-flex flex-stack flex-grow-1">
                <!--begin::Content-->
                <div class="fw-semibold">
                    <h4 class="text-gray-900 fw-bold">Attention!</h4>
                    <div class="fs-6 text-gray-700">
                        Please note that all fields in orange can not be changed by the user.
                    </div>
                </div>
                <!--end::Content-->
            </div>
            <!--end::Wrapper-->
        </div>
        <!--end::Notice-->
    </div>
    <!--end::details View-->

    <div class="col-xl-12 mb-xl-10">
        <!--begin::Input group= Passport photo-->
        <div class="d-flex justify-content-center">
            <!--begin::Image input-->
            <div class="image-input image-input-outline" data-kt-image-input="true" style="background-image: url('assets/media/svg/avatars/blank.svg')">
                <!--begin::Preview existing avatar-->
                <div class="image-input-wrapper w-200px h-200px" style="background-image: url({{ asset(\App\Http\Controllers\Shared\UserManagement\UserController::AVATAR_PATH . $picture) }})"></div>
                <!--end::Preview existing avatar-->
            </div>
            <!--end::Image input-->
        </div>
    </div>

    <!--begin::Row-->
    <div class="row gy-5 g-xl-10">

        <!--begin:: Personal Info Col-->
        <div class="col-xl-6 mb-xl-10">

            <div class="card card-flush h-lg-100">
                <!--begin::Header-->
                <div class="card-header cursor-pointer">
                    <!--begin::Title-->
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bold text-gray-700">Personal/Profile Info:</span>
                    </h3>
                    <!--end::Title-->
                </div>
                <div class="app-navbar-separator separator d-none d-lg-flex"></div>
                <!--end::Header-->

                <!--begin::Body-->
                <div class="card-body pt-5 ps-6">

                    <!--begin::Input group= User ID-->
                    <div class="fv-row mb-4">
                        <input type="text" placeholder="User ID No" name="user_idno" id="user_idno"  autocomplete="off" class="form-control bg-transparent" value="{{ auth()->user()->id }}"   disabled />
                    <!-- <div class="text-gray-500 pt-2 fw-semibold fs-6">Payroll No.</div> -->
                    </div>
                    <!--end::Input group=-->

                    <!--begin::Input group= Full name-->
                    <div class="fv-row mb-4">
                        <input type="text" placeholder="Full Name" name="full_name" id="full_name"  autocomplete="off" class="form-control bg-transparent" value="{{ auth()->user()->fullname }}"  disabled/>
                    </div>
                    <!--end::Input group=-->

                    <!--begin::Input group= Email address-->
                    <div class="fv-row mb-4">
                        <input type="text" placeholder="Email Address" name="email_add" id="email_add"  autocomplete="off" class="form-control bg-transparent" value="{{ auth()->user()->email }}"  disabled/>
                    </div>
                    <!--end::Col-->

                    <!--begin::Input group= Phone no-->
                    <div class="fv-row mb-4">
                        <input type="number" placeholder="Phone No" name="user_phone" id="user_phone" autocomplete="off" class="form-control bg-transparent" value="{{ auth()->user()->phone_no }}" disabled/>
                    </div>
                    <!--end::Col-->

                    <!--begin::Input group= Gender & Marital -->
                    <div class="col-lg-12">
                            <!--begin::Row-->
                            <div class="row">

                                <!--begin::Gender Col-->
                                <div class="col-lg-6 fv-row mb-4">
                                    <input type="text" placeholder="Gender" name="user_gender" id="user_gender" autocomplete="off" class="form-control bg-transparent" value="{{ ucfirst(auth()->user()->gender) }}" disabled/>
                                </div>
                                <!--end::Col-->

                                <!--begin::Col-->
                                <div class="col-lg-6 fv-row mb-4">
                                    <input type="text" placeholder="Marital Status" name="user_marital_status" id="user_marital_status" autocomplete="off" class="form-control bg-transparent" value="{{ ucfirst(auth()->user()->marital_status) }}" disabled/>
                                </div>
                                <!--end::Col-->

                            </div>
                            <!--end::Row-->
                        </div>

                    <!--begin::Input group= Rank-->
                    <div class="fv-row mb-4">
                        <input type="text" placeholder="Rank" name="user_rank" id="user_rank"  autocomplete="off" class="form-control bg-transparent" value="{{ auth()->user()->rank?->name }}"/>
                    </div>
                    <!--end::Input group=-->

                    {{-- <!--begin::Save Changes Button -->
                    <div class="card mb-5 mb-xl-10">
                            <!--<form class="form">-->
                                <!--begin::Actions Save Changes Button-->
                                <div class="card-footer d-flex justify-content-end py-6 px-9">
                                    <button type="submit" class="btn btn-primary" id="btn_profile_update_submit">Save Changes</button>
                                </div>
                                <!--end::Card footer
                            </form>-->
                    </div>
                    <!--end::Save Button --> --}}


                </div>
                <!--end::Body-->

            </div>

        </div>
        <!--end::Col-->

        <!--begin:: Password Reset Col-->
        <div class="col-xl-6 mb-xl-10" id="kt_password_update">
            <form class="form-horizontal" role="form"  method="POST" id="kt_password_update_form"
                action="{{ route('password.update') }}">

                <div class="card card-flush h-lg-100">
                    <!--begin::Header-->
                    <div class="card-header cursor-pointer">
                        <!--begin::Title-->
                        <h3 class="card-title align-items-start flex-column">
                            <span class="card-label fw-bold text-gray-700">Password Reset:</span>
                        </h3>
                        <!--end::Title-->
                    </div>
                    <div class="app-navbar-separator separator d-none d-lg-flex"></div>
                    <!--end::Header-->

                    <!--begin::Body-->
                    <div class="card-body pt-5 ps-6">

                        <!--begin::Input group : New password-->
                        <div class="fv-row mb-8" data-kt-password-meter="true">
                            <!--begin::Wrapper-->
                            <div class="mb-1">
                                <!--begin::Input wrapper-->
                                <div class="position-relative mb-3">
                                    <input class="form-control bg-transparent" type="password" placeholder="Password" name="password" id="password"  autocomplete="off" />
                                    <span class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2" data-kt-password-meter-control="visibility">
                                        <i class="ki-outline ki-eye-slash fs-2"></i>
                                        <i class="ki-outline ki-eye fs-2 d-none"></i>
                                    </span>
                                </div>
                                <!--end::Input wrapper-->

                                <!--begin::Meter-->
                                <div class="d-flex align-items-center mb-3" data-kt-password-meter-control="highlight">
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px me-2"></div>
                                    <div class="flex-grow-1 bg-secondary bg-active-success rounded h-5px"></div>
                                </div>
                                <!--end::Meter-->
                            </div>
                            <!--end::Wrapper-->
                            <!--begin::Hint-->
                            <div class="text-muted">Use 8 or more characters with a mix of letters, numbers & symbols.</div>
                            <!--end::Hint-->
                        </div>
                        <!--end::Input group=-->

                        <!--end::Input group : Repeat password-->
                        <div class="fv-row mb-8">
                            <!--begin::Repeat Password-->
                            <input type="password" placeholder="Repeat Password" name="password_confirmation"  id="password_confirmation" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Repeat Password-->
                        </div>
                        <!--end::Input group=-->

                        <!--begin::Save Changes Button -->
                        <div class="card mb-5 mb-xl-10">
                                <!--<form class="form">-->
                                    <!--begin::Actions Save Changes Button-->
                                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                                        <button type="submit" class="btn btn-primary" data-kt-password-action="submit">
                                            <span class="indicator-label">Change Password</span>
                                            <span class="indicator-progress">Please wait...
                                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                        </button>
                                    </div>
                                    <!--end::Card footer
                                </form>-->
                        </div>
                        <!--end::Save Button -->

                    </div>
                    <!--end::Body-->
                </div>

            </div>
            <!--end::Col-->
        </form>
    </div>
    <!--end::Row-->

    @push('footer')
        @vite('resources/js/shared/my-profile/my-profile.js')
    @endpush
</x-app-layout>

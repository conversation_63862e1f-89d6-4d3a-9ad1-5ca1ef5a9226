@props(['maintenance', 'actions', 'forwarders'])
@php
    $breadCrumbs = [
        ['name' => __('Home'), 'url' => route('dashboard')],
        ['name' => __('Estate')],
        ['name' => __('Maintenance')],
        ['name' => __('External Maintenance'), 'url' => route('estate.maintenance.external.index')],
        ['name' => __("#{$maintenance->id}"), 'url' => route('estate.maintenance.external.show', [$maintenance->id])],
    ];
    // dd($maintenance->id);
@endphp

<x-app-layout :title="__('Maintenance Details')" :breadCrumbs="$breadCrumbs">
    @push('header')
        {{-- <link href="{{asset('assets/plugins/custom/datatables/datatables.bundle.css')}}" rel="stylesheet" type="text/css"> --}}
    @endpush

    <!--begin::Products-->
    <div class="card card-flush border border-gray-300">
        <!--begin::Card header-->
        <div class="card-header align-items-center py-5 gap-2 gap-md-5">
            <!--begin::Card title-->
            <div class="card-title">
                <!--begin::Heading-->
                <div class="text-center">
                    <!--begin::Title-->
                    <h1 class="mb-3">Manage External Maintenance: #{{$maintenance->id}}</h1>
                    <!--end::Title-->
                    <!--begin::Description-->
                    <div class="text-gray-500 fw-semibold fs-5">Perform actions on external maintenance entry.</div>
                    <!--end::Description-->
                </div>
                <!--end::Heading-->
            </div>
            <!--end::Card title-->
            <!--begin::Card toolbar-->
            <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
            </div>
            <!--end::Card toolbar-->
        </div>
        <!--end::Card header-->
        <!--begin::Card body-->
        <div class="card-body pt-0">

            <!--begin:: Summary Table-->
            <div class="table align-middle table-row-dashed fs-6 gy-5">
                <table class="table table-bordered table-rounded border-gray-300">
                    <thead>
                        <th colspan="2">
                            <!--begin::Title-->
                            <h3 class="fw-bold text-success m-0">
                                <i class="ki-outline ki-document fs-3 text-success me-2"></i>Entry Details:
                            </h3>
                            <!--end::Title-->
                        </th>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="col-md-3"><label class="fs-6 fw-semibold">Entry Date:</label></td>
                            <td>{{ $maintenance->entry_date->format('d-M-Y') }}</td>
                        </tr>
                        <tr>
                            <td class="col-md-3"><label class="fs-6 fw-semibold">Requesting Officer:</label></td>
                            <td>{{ $maintenance->requesting_officer_name }}</td>
                        </tr>
                        <tr>
                            <td class="col-md-3"><label class="fs-6 fw-semibold">Department:</label></td>
                            <td>{{ $maintenance->department->name }}</td>
                        </tr>
                        <tr>
                            <td class="col-md-3"><label class="fs-6 fw-semibold">Location:</label></td>
                            <td>{{ $maintenance->location }}</td>
                        </tr>
                        <tr>
                            <td class="col-md-3"><label class="fs-6 fw-semibold">Work Type:</label></td>
                            <td>{{ $maintenance->workType->name }}</td>
                        </tr>
                        <tr>
                            <td class="col-md-3"><label class="fs-6 fw-semibold">Description of Work:</label></td>
                            <td>{{ $maintenance->description }}</td>
                        </tr>
                        <tr>
                            <td class="col-md-3"><label class="fs-6 fw-semibold">Proposed Action:</label></td>
                            <td>{{ $maintenance->proposed_action }}</td>
                        </tr>
                        <tr>
                            <td class="col-md-3"><label class="fs-6 fw-semibold">Cost Implication:</label></td>
                            <td>₦ {{ number_format($maintenance->cost_implication, 2) }}</td>
                        </tr>
                        <tr>
                            <td class="col-md-3"><label class="fs-6 fw-semibold">Checked By:</label></td>
                            <td>{{ $maintenance->checkedBy->fullname }}</td>
                        </tr>

                    </tbody>
                </table>
            </div>
            <!--end:: Summary Table-->

            <!--begin::Progress Bar-->
            <div class="vis-group mt-2 mb-5">
                <div class="vis-item vis-range vis-readonly"
                    style="transform: translateX(10px); width: 98%; top: 17.5px;">
                    <div class="vis-item-overflow">
                        <div class="vis-item-content" style="transform: translateX(0px);">
                            <div
                                class="rounded-pill bg-light-primary d-flex align-items-center position-relative h-25px w-100 p-2 overflow-hidden">
                                @php
                                    $percentage = $maintenance->workflow->stage_progress_percent;
                                    $percentageColor = match (true) {
                                        $percentage >= 80 => 'success',
                                        ($percentage >= 60 && $percentage < 80) => 'info',
                                        ($percentage >= 40 && $percentage < 60) => 'primary',
                                        ($percentage >= 20 && $percentage < 40) => 'warning',
                                        $percentage < 20 => 'danger',
                                        default => 'light',
                                    };
                                @endphp
                                <div class="position-absolute rounded-pill d-block bg-{{ $percentageColor }} start-0 top-0 h-100 z-index-1"
                                    style="width:{{ $maintenance->workflow->stage_progress_percent }}%;"></div>

                                <div class="d-flex align-items-center position-relative z-index-2">
                                    <span class="fw-bold fs-6">
                                        Status: {{ ucwords($maintenance->status) }}
                                    </span>
                                </div>

                                <div
                                    class="d-flex flex-center bg-body rounded-pill fs-7 fw-bolder ms-auto h-100 px-3 position-relative z-index-2">
                                    {{ $maintenance->workflow->stage_progress_percent }}%
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Progress Bar-->

            <!--begin::Row-->
            <div class="row gy-5 g-xl-10">
                <!--begin::Col-->
                <div class="col-xl-6 mb-xl-10">
                    <!--begin::Table widget 2-->
                    <div class="card border border-gray-300 h-md-100">
                        <!--begin::Header-->
                        <div class="card-header align-items-center border-0">
                            <!--begin::Title-->
                            <h3 class="fw-bold text-success m-0">Action History</h3>
                            <!--end::Title-->
                        </div>
                        <!--end::Header-->
                        <!--begin::Body-->
                        <div class="card-body pt-2">
                            <!--begin::Tab Content-->
                            <div class="tab-content">
                                <!--begin::Table container-->
                                <div class="table-responsive">
                                    <!--begin::Table-->
                                    <table class="table align-middle table-row-dashed fs-6 gy-3" id="kt_updates_table">
                                        <thead>
                                            <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                                                <th class="text-start w-10px text-gray-800">S/N</th>
                                                <th class="text-start min-w-50px text-gray-800">Date</th>
                                                <th colspan="2" class="text-start min-w-50px text-gray-800">Action</th>
                                                <th class="text-start min-w-50px text-gray-800">Forwarded To</th>
                                                <th class="text-end"></th>
                                            </tr>
                                        </thead>
                                        <tbody class="fw-bold text-gray-600">
                                            @foreach ($maintenance->updates as $update)
                                                <tr>
                                                    <td class="text-start" data-kt-update-id="{{ $update->id }}">
                                                        <span class="fw-normal">{{ $loop->index + 1 }}</span>
                                                    </td>
                                                    <td class="text-start">
                                                        <span>{{ $update->created_at->diffForHumans() }}</span>
                                                    </td>
                                                    <td colspan='2' class="text-start">
                                                        <span class="badge badge-light-{{ $update->action->color }}">{{ $update->action->name }}</span>
                                                    </td>
                                                    <td class="text-start pe-0">
                                                        <span class="fw-normal">{{ $update->forwardTo ? $update->forwardTo->fullname : 'None' }}</span>
                                                    </td>
                                                    <td class="text-end">
                                                        <button type="button"
                                                            class="btn btn-sm btn-icon btn-light btn-active-light-primary toggle h-25px w-25px"
                                                            data-kt-updates-filter="expand_row">
                                                            <i class="ki-outline ki-plus fs-4 m-0 toggle-off"></i>
                                                            <i class="ki-outline ki-minus fs-4 m-0 toggle-on"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr data-kt-updates-detail-row="{{ $update->id }}" class="d-none">
                                                    <td colspan="6">
                                                        <div class="d-flex pt-2">
                                                            @php
                                                            $picture = $update->createdBy->profile_picture ?? 'default.jpg';
                                                            @endphp
                                                            <!--begin::Avatar-->
                                                            <div class="symbol symbol-45px me-5">
                                                                <img src="{{ asset(\App\Http\Controllers\Shared\UserManagement\UserController::AVATAR_PATH . $picture) }}" alt="{{ $update->createdBy->fullname }}" />
                                                            </div>
                                                            <!--end::Avatar-->
                                                            <!--begin::Wrapper-->
                                                            <div class="d-flex flex-column flex-row-fluid">
                                                                <!--begin::Info-->
                                                                <div class="d-flex align-items-center flex-wrap mb-0">
                                                                    <!--begin::Name-->
                                                                    <span class="text-gray-800 text-hover-primary fw-bold me-6">{{ $update->createdBy->fullname }}</span>
                                                                    <!--end::Name-->
                                                                    <!--begin::Reply-->
                                                                    <span class="ms-auto text-gray-500 text-hover-primary fw-semibold fs-7">{{ $update->created_at->diffForHumans() }}</span>
                                                                    <!--end::Reply-->
                                                                </div>
                                                                <!--end::Info-->
                                                                <!--begin::Text-->
                                                                <span class="fs-6 fw-normal text-gray-700 mb-3">{{ $update->comments }}</span>
                                                                <!--end::Text-->
                                                                @if (!empty($update->supporting_doc_url) && $update->supporting_doc_url !== "[]")
                                                                @php
                                                                    $docs = is_array($update->supporting_doc_url)
                                                                        ? $update->supporting_doc_url
                                                                        : (is_string($update->supporting_doc_url) ? json_decode($update->supporting_doc_url, true) : []);
                                                                @endphp
                                                                @if ($docs && count($docs))
                                                                    <div class="d-flex flex-wrap gap-3 mb-3">
                                                                        @foreach ($docs as $doc)
                                                                            @php
                                                                                $ext = strtolower(pathinfo($doc, PATHINFO_EXTENSION));
                                                                                $url = asset(\App\Http\Controllers\Estate\Estate\EstateExternalMaintenanceController::STORAGE_PATH . $doc);
                                                                            @endphp
                                                                            <div class="position-relative m-1 shadow-sm bg-white rounded d-flex flex-column align-items-center justify-content-center" style="width:90px;min-height:90px;border:1px solid #e5e5e5;overflow:hidden;flex:0 0 auto;">
                                                                                @if (in_array($ext, ['jpg', 'jpeg', 'png']))
                                                                                    <a href="{{ $url }}" target="_blank">
                                                                                        <img src="{{ $url }}" style="width:88px;height:88px;object-fit:cover;border-radius:6px;" />
                                                                                    </a>
                                                                                @elseif ($ext === 'pdf')
                                                                                    <a href="{{ $url }}" target="_blank" class="d-flex flex-column align-items-center justify-content-center" style="width:88px;height:88px;text-decoration:none;">
                                                                                        <i class="ki-outline ki-file fs-2"></i>
                                                                                        <span class="fs-8">{{ strtoupper($ext) }}</span>
                                                                                        <span class="fs-8 text-truncate" style="max-width:80px;">{{ basename($doc) }}</span>
                                                                                    </a>
                                                                                @else
                                                                                    <a href="{{ $url }}" target="_blank">{{ basename($doc) }}</a>
                                                                                @endif
                                                                            </div>
                                                                        @endforeach
                                                                    </div>
                                                                @endif
                                                            @endif
                                                            </div>
                                                            <!--end::Wrapper-->
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                    <!--end::Table-->
                                </div>
                                <!--end::Table container-->
                            </div>
                            <!--end::Tab Content-->
                        </div>
                        <!--end: Card Body-->
                    </div>
                    <!--end::Table widget 2-->
                </div>
                <!--end::Col-->
                @if (\App\Services\Shared\WorkflowService::hasAccessToEntryBasedOnWorkflowStage($maintenance->workflow, auth()->user()->id) !== false )
                    <!--begin::Col-->
                    <div class="col-xl-6 mb-5 mb-xl-10">
                        <!--begin::Chart widget 4-->
                        <div class="card border border-gray-300 card-flush overflow-hidden h-md-100">
                            <!--begin::Header-->
                            <div class="card-header py-5">
                                <!--begin::Title-->
                                <h3 class="card-title align-items-start flex-column">
                                    <span class="card-label fw-bold text-primary">Manage/Update</span>
                                </h3>
                                <!--end::Title-->
                                <!--begin::Toolbar-->
                                <div class="card-toolbar">
                                </div>
                                <!--end::Toolbar-->
                            </div>
                            <!--end::Header-->
                            <!--begin::Card body-->
                            <div class="card-body pt-2">
                                @if ($hasNextStage)
                                    <!--begin::Tab Content-->
                                    <div class="tab-content" id="kt_maintenance_update">

                                        <form id="kt_maintenance_add_update_form" class="form" enctype="multipart/form-data">
                                            @csrf
                                            <input type="hidden" name="updatable_id" value="{{ $maintenance->id }}" autocomplete="off">
                                            <input type="hidden" name="workflow_id" value="{{ $maintenance->workflow->id }}"
                                                data-menu-id="{{ $maintenance->workflow->menu_id }}"
                                                data-current-stage="{{ $maintenance->workflow->stage_number }}"
                                                autocomplete="off">

                                            <!--begin::Input group : Select Action-->
                                            <div class="d-flex flex-column mb-8 fv-row">
                                                <!--begin::Col : Action-->
                                                <label class="required fs-6 fw-semibold mb-2">Action</label>
                                                <select class="form-select form-select-solid" data-control="select2"
                                                    data-hide-search="true" data-placeholder="Select action" name="action_id">
                                                    <option value="">Select a action ...</option>
                                                    @foreach ($actions as $action)
                                                        @php
                                                            $selected = $action->id == old('action_id');
                                                        @endphp
                                                        <option value="{{ $action->id }}" {{ $selected ? 'selected="selected"' : '' }} data-kt-action-status="{{ $action->status }}">
                                                            {{ $action->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <!--end::Col-->
                                            </div>
                                            <!--end::Input group-->

                                            <!--begin::Input group : Comment/Notes -->
                                            <div class="d-flex flex-column mb-8 fv-row">
                                                <label class="fs-6 fw-semibold mb-2">Comment/Notes</label>
                                                <textarea class="form-control form-control-solid" rows="2" name="comments"
                                                    placeholder="Type additonal comment/notes here"></textarea>
                                            </div>
                                            <!--end::Input group-->

                                            @if ($maintenance->workflow->attachment_required)
                                                <!--begin::Input group : Support Docs Attachments-->
                                                <div class="d-flex flex-column align-items-start mb-7">
                                                    <label class="fs-6 fw-semibold form-label mb-4">
                                                        <span class="required">Support Docs Attachments</span>
                                                        <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                                                            data-bs-content="This field is required.">
                                                            <i class="ki-outline ki-information fs-7"></i>
                                                        </span>
                                                    </label>
                                                    <div class="mb-2">
                                                        <label for="update_supporting_doc_url" class="btn btn-primary d-inline-flex align-items-center gap-2">
                                                            <i class="ki-outline ki-upload fs-3"></i>
                                                            <span>Upload Support Docs</span>
                                                        </label>
                                                        <input type="file" id="update_supporting_doc_url" name="supporting_doc_url[]" accept=".png,.jpg,.jpeg,.pdf" multiple class="d-none" />
                                                    </div>
                                                    <div id="update-supporting-docs-preview" class="d-flex flex-wrap gap-2 mt-2">
                                                    </div>
                                                    <div class="form-text">Allowed file types: png, jpg, jpeg, pdf. Size limit is 2MB. Upload up to 10 files.</div>
                                                </div>
                                                <!--end::Input group-->
                                            @endif

                                            <!--begin::Input group : Escalate/de-escalate to-->
                                            <div class="d-flex flex-column mb-8 fv-row">
                                                <!--begin::Col : Escalate/de-escalate-->
                                                <label class="required fs-6 fw-semibold mb-2">Escalate/de-escalate to</label>
                                                <div class="d-flex gap-3">
                                                    <select class="form-select form-select-solid" data-control="select2"
                                                        data-hide-search="true" data-placeholder="None" name="forward_to">
                                                        <option value="">Select ...</option>
                                                        @foreach ($forwarders as $forwarder)
                                                            @php
                                                                // $forwarder->load('rank');
                                                                $selected = $forwarder->id == old('forward_to');
                                                            @endphp
                                                            <option value="{{ $forwarder->id }}" {{ $selected ? 'selected="selected"' : '' }}>
                                                                {{ $forwarder->rank?->name }} ({{ $forwarder->fullname }})
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    <button type="button" class="btn btn-icon btn-light-danger" data-kt-maintenance-update-action="forward-to-reset">
                                                        <i class="ki-outline ki-cross-circle fs-1"></i>
                                                    </button>
                                                </div>
                                                <!--end::Col-->
                                            </div>
                                            <!--end::Input group-->

                                            <!--begin::Actions : SUBMIT/CANCEL BUTTON-->
                                            <div class="text-center pt-5">
                                                <button type="reset" class="btn btn-light me-3"
                                                    data-kt-maintenance-update-action="reset">Reset</button>
                                                <button type="submit" class="btn btn-primary"
                                                    data-kt-maintenance-update-action="submit">
                                                    <span class="indicator-label">Submit</span>
                                                    <span class="indicator-progress">
                                                        Please wait...
                                                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                                    </span>
                                                </button>
                                            </div>
                                            <!--end::Actions-->

                                        </form>
                                    </div>
                                    <!--end::Tab Content-->
                                @else
                                    <div class="text-center">
                                        <h3 class="fw-bold text-success m-0">No Further Actions Available</h3>
                                    </div>
                                @endif
                            </div>
                            <!--end::Card body-->
                        </div>
                        <!--end::Chart widget 4-->
                    </div>
                    <!--end::Col-->
                @endif
            </div>
            <!--end::Row-->
        </div>
        <!--end::Card body-->
    </div>
    <!--end::Products-->


    @push('footer')
        @vite('resources/js/estate/maintenance/view-update-maintenance.js')
        @vite('resources/js/estate/maintenance/add-update-maintenance.js')
    @endpush

</x-app-layout>

@props(['maintenance', 'departments', 'workTypes', 'users'])
@php
    $breadCrumbs = [
        ['name' => __('Home'), 'url' => route('dashboard')],
        ['name' => __('Estate')],
        ['name' => __('Maintenance')],
        ['name' => __('Internal Maintenance'), 'url' => route('estate.maintenance.internal.index')],
        ['name' => __("#{$maintenance->id}"), 'url' => route('estate.maintenance.internal.edit', [$maintenance->id])],
    ];
    // dd($maintenance, $departments, $workTypes, $users);
@endphp

<x-app-layout :title="__('Edit Estate Maintenance (Internal)')" :breadCrumbs="$breadCrumbs">
    <!--begin::Content-->
    <div class="card card-flush border border-gray-300">
        <!--begin::Card header-->
        <div class="card-header">
            <!--begin::Card title-->
            <div class="card-title">
                <h2>Maintenance Requisition Entry: #{{ $maintenance->id }}</h2>
            </div>
            <!--end::Card title-->
        </div>
        <!--end::Card header-->

        <!--begin::Card body-->
        <div class="card-body" id="kt_update_maintenance">
            <form id="kt_update_maintenance_form" class="form" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="_maintenance" value="{{ $maintenance->id }}" autocomplete="off">
                <input type="hidden" name="type" value="internal" autocomplete="off">

                <!--begin::Input group : Entry Date & Requesting Officer Name-->
                <div class="row g-9 mb-8">
                    <!--begin::Col : Entry Date-->
                    <div class="col-md-4 fv-row">
                        <label class="required fs-6 fw-semibold mb-2">Entry Date</label>
                        <!--begin::Input-->
                        <div class="position-relative d-flex align-items-center">
                            <!--begin::Icon-->
                            <div class="symbol symbol-20px me-4 position-absolute ms-4">
                                <span class="symbol-label bg-secondary">
                                    <i class="ki-outline ki-element-11"></i>
                                </span>
                            </div>
                            <!--end::Icon-->
                            <!--begin::Datepicker-->
                            <input class="form-control form-control-solid ps-12" placeholder="Select a date" name="entry_date" value="{{ old('entry_date', $maintenance->entry_date->format('d-M-Y')) }}" />
                            <!--end::Datepicker-->
                        </div>
                        <!--end::Input-->
                    </div>
                    <!--end::Col-->
                    <!--begin::Col : Requesting Officer Name-->
                    <div class="col-md-8 fv-row">
                        <!--begin::Label-->
                        <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                            <span class="required">Requesting Officer Name</span>
                        </label>
                        <!--end::Label-->
                        <input type="text" class="form-control form-control-solid"
                            placeholder="Enter requesting officer name" name="requesting_officer_name" value="{{ old('requesting_officer_name', $maintenance->requesting_officer_name) }}"/>
                        <!--end::Col-->
                    </div>
                    <!--end::Input group-->
                </div>

                <!--begin::Input group : Department & Location-->
                <div class="row g-9 mb-8">
                    <!--begin::Col : Department-->
                    <div class="col-md-4 fv-row">
                        <label class="required fs-6 fw-semibold mb-2">Department</label>
                        <select class="form-select form-select-solid" data-control="select2" data-hide-search="true"
                            data-placeholder="Select a department" name="department_id">
                            <option value="">Select a department...</option>
                            @foreach ($departments as $department)
                                @php
                                    $selected = $department->id == old('department_id', $maintenance->department_id);
                                @endphp
                                <option value="{{ $department->id }}" {{ $selected ? 'selected="selected"' : '' }}>
                                    {{ $department->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <!--end::Col-->
                    <!--begin::Col : Location-->
                    <div class="col-md-8 fv-row">
                        <!--begin::Label-->
                        <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                            <span class="required">Location</span>
                        </label>
                        <!--end::Label-->
                        <input type="text" class="form-control form-control-solid"
                            placeholder="Enter maintenance location" name="location" value="{{ old('location', $maintenance->location) }}" />
                        <!--end::Col-->
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Input group-->

                <!--begin::Input group : Work Type & Others (work type)-->
                <div class="row g-9 mb-8">
                    <!--begin::Col : Work Type-->
                    <div class="col-md-6 fv-row">
                        <label class="required fs-6 fw-semibold mb-2">Work Type</label>
                        <select class="form-select form-select-solid" data-control="select2" data-hide-search="true"
                            data-placeholder="Select work type" name="work_type_id">
                            <option value="">Select a work type...</option>
                            @foreach ($workTypes as $worktype)
                                @php
                                    $selected = $worktype->id == old('worktype_id', $maintenance->work_type_id);
                                @endphp
                                <option value="{{ $worktype->id }}" {{ $selected ? 'selected="selected"' : '' }}>
                                    {{ $worktype->name }}
                                    @if (str_contains(strtolower($worktype->name), 'other'))
                                        "(Please type)"
                                    @endif
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <!--end::Col-->
                    <!--begin::Col : Others (work type)-->
                    <div class="col-md-6 fv-row">
                        <!--begin::Label-->
                        <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                            <span>Others (work type)</span>
                        </label>
                        <!--end::Label-->
                        <input type="text" class="form-control form-control-solid" placeholder=""
                            name="other_work_type" value="{{ old('other_work_type', $maintenance->other_work_type) }}" />
                        <!--end::Col-->
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Input group-->

                <!--begin::Input group : Description of Work -->
                <div class="d-flex flex-column mb-8 fv-row">
                    <label class="required fs-6 fw-semibold mb-2">Description of Work</label>
                    <textarea class="form-control form-control-solid" rows="3" name="description"
                        placeholder="Type your description of work here">{{ old('description', $maintenance->description) }}</textarea>
                </div>
                <!--end::Input group-->

                <!--begin::Input group : Proposed Action -->
                <div class="d-flex flex-column mb-8 fv-row">
                    <label class="required fs-6 fw-semibold mb-2">Proposed Action</label>
                    <textarea class="form-control form-control-solid" rows="2" name="proposed_action"
                        placeholder="Type the proposed action here">{{ old('proposed_action', $maintenance->proposed_action) }}</textarea>
                </div>
                <!--end::Input group-->

                <!--begin::Input group : Cost Implication & Checked By-->
                <div class="row g-9 mb-8">
                    <!--begin::Col : Cost Implication-->
                    <div class="col-md-6 fv-row">
                        <!--begin::Label-->
                        <label class="d-flex align-items-center fs-6 fw-semibold mb-2">
                            <span class="required">Cost Implication</span>
                        </label>
                        <!--end::Label-->
                        <input type="number" class="form-control form-control-solid" placeholder=""
                            name="cost_implication" value="{{ old('cost_implication', $maintenance->cost_implication) }}" />
                        <!--end::Col-->
                    </div>
                    <!--end::Col-->
                    <!--begin::Col : Checked By-->
                    <div class="col-md-6 fv-row">
                        <!--begin::Label-->
                        <label class="required d-flex align-items-center fs-6 fw-semibold mb-2">
                            <span>Checked By</span>
                        </label>
                        <!--end::Label-->
                        <select class="form-select form-select-solid" data-control="select2" data-hide-search="true"
                            data-placeholder="Select user" name="checked_by">
                            <option value="">Select a user...</option>
                            @foreach ($users as $user)
                                @php
                                    $selected = $user->id == old('user_id', $maintenance->checked_by);
                                @endphp
                                <option value="{{ $user->id }}" {{ $selected ? 'selected="selected"' : '' }}>
                                    {{ $user->fullname }}
                                </option>
                            @endforeach
                        </select>
                        <!--end::Col-->
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Input group-->

                @if ($maintenance->workflow->attachment_required)
                    <!--begin::Input group : Support Docs Attachments-->
                    <div class="fv-row mb-8">
                        <label class="fs-6 fw-semibold mb-2">Support Docs Attachments</label>

                        <!-- Existing Documents Preview -->
                        @if (!empty($maintenance->supporting_doc_url))
                            @php
                                $docs = is_array($maintenance->supporting_doc_url)
                                    ? $maintenance->supporting_doc_url
                                    : (is_string($maintenance->supporting_doc_url) ? json_decode($maintenance->supporting_doc_url, true) : []);
                            @endphp
                            @if ($docs && count($docs))
                                <div id="existing-supporting-docs" class="d-flex flex-wrap gap-3 mb-3">
                                    @foreach ($docs as $doc)
                                        @php
                                            $ext = strtolower(pathinfo($doc, PATHINFO_EXTENSION));
                                            $url = asset(\App\Http\Controllers\Estate\Estate\EstateInternalMaintenanceController::STORAGE_PATH . $doc);
                                        @endphp
                                        <div class="position-relative m-1 shadow-sm bg-white rounded d-flex flex-column align-items-center justify-content-center" style="width:90px;min-height:90px;border:1px solid #e5e5e5;overflow:hidden;flex:0 0 auto;">
                                            @if (in_array($ext, ['jpg', 'jpeg', 'png']))
                                                <a href="{{ $url }}" target="_blank">
                                                    <img src="{{ $url }}" style="width:88px;height:88px;object-fit:cover;border-radius:6px;" />
                                                </a>
                                            @elseif ($ext === 'pdf')
                                                <a href="{{ $url }}" target="_blank" class="d-flex flex-column align-items-center justify-content-center" style="width:88px;height:88px;text-decoration:none;">
                                                    <i class="ki-outline ki-file fs-2"></i>
                                                    <span class="fs-8">{{ strtoupper($ext) }}</span>
                                                    <span class="fs-8 text-truncate" style="max-width:80px;">{{ basename($doc) }}</span>
                                                </a>
                                            @else
                                                <a href="{{ $url }}" target="_blank">{{ basename($doc) }}</a>
                                            @endif
                                            <button type="button" class="btn btn-xs btn-danger position-absolute top-0 end-0 m-1 px-1 py-0 delete-existing-doc" data-filename="{{ $doc }}" style="border-radius:50%;width:22px;height:22px;line-height:1;z-index:2;">
                                                <i class="ki-outline ki-cross fs-6"></i>
                                            </button>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        @endif

                        <!--begin::Styled File Input Button for new uploads-->
                        <div class="mb-2">
                            <label for="supporting_doc_url" class="btn btn-primary d-inline-flex align-items-center gap-2">
                                <i class="ki-outline ki-upload fs-3"></i>
                                <span>Upload Support Docs</span>
                            </label>
                            <input type="file" id="supporting_doc_url" name="supporting_doc_url[]" accept=".png,.jpg,.jpeg,.pdf" multiple class="d-none" />
                        </div>
                        <div id="supporting-docs-preview" class="d-flex flex-wrap gap-2 mt-2"></div>
                        <input type="hidden" name="supporting_doc_urls" id="supporting_doc_urls">
                        <div class="form-text">Allowed file types: png, jpg, jpeg, pdf. Size limit is 2MB. Upload up to 10 files.</div>
                        <!--end::File Input-->
                    </div>
                    <!--end::Input group-->
                @endif

                <!--begin::Input group : Notifications SMS Phone In-app -->
                <div class="mb-15 fv-row">
                    <!--begin::Wrapper-->
                    <div class="d-flex flex-stack">
                        <!--begin::Label-->
                        <div class="fw-semibold me-5">
                            <label class="fs-6">Notifications</label>
                            <div class="fs-7 text-gray-500">Allow Notifications by Phone or Email or In-app</div>
                        </div>
                        <!--end::Label-->
                        <!--begin::Checkboxes-->
                        <div class="d-flex align-items-center">
                            <!--begin::Checkbox-->
                            <label class="form-check form-check-custom form-check-solid me-10">
                                @php
                                    $checked = old('email', $maintenance->notify_by_email);
                                @endphp
                                <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]"
                                    value="email" {{ $checked ? 'checked="checked"' : '' }} disabled />
                                <span class="form-check-label fw-semibold">Email</span>
                            </label>
                            <!--end::Checkbox-->
                            <!--begin::Checkbox-->
                            <label class="form-check form-check-custom form-check-solid me-10">
                                @php
                                    $checked = old('phone', $maintenance->notify_by_phone);
                                @endphp
                                <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]"
                                    value="phone" {{ $checked ? 'checked="checked"' : '' }} disabled />
                                <span class="form-check-label fw-semibold">Phone</span>
                            </label>
                            <!--end::Checkbox-->
                            <!--begin::Checkbox : In-app -->
                            <label class="form-check form-check-custom form-check-solid me-10">
                                @php
                                    $checked = old('inapp', $maintenance->notify_in_app);
                                @endphp
                                <input class="form-check-input h-20px w-20px" type="checkbox" name="notifications[]"
                                    value="inapp" {{ $checked ? 'checked="checked"' : '' }} />
                                <span class="form-check-label fw-semibold">In-app</span>
                            </label>
                            <!--end::Checkbox-->
                        </div>
                        <!--end::Checkboxes-->
                    </div>
                    <!--end::Wrapper-->
                </div>
                <!--end::Input group-->

                <div class="text-center pt-15">
                    <a href="{{ url()->previous() }}" class="btn btn-light me-3">Cancel</a>
                    <button type="submit" class="btn btn-primary" data-kt-maintenance-action="submit">
                        <span class="indicator-label">Update Maintenance Entry</span>
                        <span class="indicator-progress">Please wait...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                    </button>
                </div>
            </form>
        </div>
        <!--end::Card body-->
    </div>
    <!--end::Content-->

    @push('footer')
        @vite('resources/js/estate/maintenance/update-maintenance.js')
        {{-- <script src="{{asset('assets/js/custom/apps/estate/maintenance/update-maintenance.js')}}"></script> --}}
    @endpush
</x-app-layout>

"use strict";
var KTProfilePasswordUpdate = (function () {
    const section = document.getElementById("kt_password_update");
    const form = section.querySelector("#kt_password_update_form");
    const submitBtn = form.querySelector('[data-kt-password-action="submit"]');
    return {
        init: function () {
            (() => {
                var validation = FormValidation.formValidation(form, {
                    fields: {
                        password: {
                            validators: {
                                notEmpty: { message: "Password is required" },
                                stringLength: {
                                    min: 8,
                                    message: "Password must be at least 8 characters",
                                },
                            },
                        },
                        password_confirmation: {
                            validators: {
                                notEmpty: {
                                    message: "Password confirmation is required",
                                },
                                identical: {
                                    compare: function () {
                                        return e.querySelector('[name="password"]').value;
                                    },
                                    message: "The password and its confirmation do not match",
                                },
                            },
                        },
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: "",
                        }),
                    },
                });
                submitBtn.addEventListener("click", (t) => {
                    t.preventDefault();
                    validation && validation.validate().then(function (t) {
                        if ("Valid" == t) {
                            submitBtn.setAttribute("data-kt-indicator", "on"),
                            (submitBtn.disabled = !0);
                            // Use FormData for file upload
                            const formData = new FormData(form);
                            axios
                                .post(form.action, formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                        "Content-Type": "multipart/form-data"
                                    },
                                })
                                .then(function (response) {
                                    submitBtn.removeAttribute("data-kt-indicator");
                                    submitBtn.disabled = !1;
                                    Swal.fire({
                                        text: "User has been successfully added!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText:
                                            "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    }).then(function (t) {
                                        t.isConfirmed && n.hide();
                                        window.location.reload();
                                    });
                                })
                                .catch(function (error) {
                                    submitBtn.removeAttribute("data-kt-indicator");
                                    submitBtn.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors =
                                            error.response.data.errors;
                                        let errorList = "<ul>";
                                        for (let key in errors) {
                                            if (errors.hasOwnProperty(key)) {
                                                errors[key].forEach((msg) => {
                                                    errorList += `<li>${msg}</li>`;
                                                });
                                            }
                                        }
                                        errorList += "</ul>";
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        } else {
                            Swal.fire({
                                text: "Sorry, looks like there are some errors detected, please try again.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                },
                            });
                        }
                    });
                });
            })();
            (() => {
                document.getElementById("user_idno").style.border = "2px solid orange";
                document.getElementById("full_name").style.border = "2px solid orange";
                document.getElementById("email_add").style.border = "2px solid orange";
                document.getElementById("user_phone").style.border = "2px solid orange";
                document.getElementById("user_gender").style.border = "2px solid orange";
                document.getElementById("user_marital_status").style.border = "2px solid orange";
                document.getElementById("user_rank").style.border = "2px solid orange";
            })();
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTProfilePasswordUpdate.init();
});

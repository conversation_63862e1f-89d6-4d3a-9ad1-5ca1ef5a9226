"use strict";
var KTUsersUpdateUser = (function () {
    const t = document.getElementById("kt_update_user"),
        e = t.querySelector("#kt_update_user_form");
    return {
        init: function () {
            (() => {
                var o = FormValidation.formValidation(e, {
                    fields: {
                        fullname: {
                            validators: {
                                notEmpty: { message: "Full name is required" },
                            },
                        },
                        email: {
                            validators: {
                                notEmpty: { message: "Email is required" },
                                emailAddress: {
                                    message:
                                        "The value is not a valid email address",
                                },
                            },
                        },
                        phone_no: {
                            validators: {
                                notEmpty: {
                                    message: "Phone number is required",
                                },
                            },
                        },
                        gender: {
                            validators: {
                                notEmpty: {
                                    message: "Gender is required",
                                },
                            },
                        },
                        marital_status: {
                            validators: {
                                notEmpty: {
                                    message: "Marital Status is required",
                                },
                            },
                        },
                        // password: { ... } // Optional: add password validation if needed
                    },
                    plugins: {
                        trigger: new FormValidation.plugins.Trigger(),
                        bootstrap: new FormValidation.plugins.Bootstrap5({
                            rowSelector: ".fv-row",
                            eleInvalidClass: "",
                            eleValidClass: "",
                        }),
                    },
                });
                const i = t.querySelector('[data-kt-users-action="submit"]');
                i.addEventListener("click", (t) => {
                    t.preventDefault();
                    o && o.validate().then(function (t) {
                        if ("Valid" == t) {
                            i.setAttribute("data-kt-indicator", "on"),
                            (i.disabled = !0);
                            // Use FormData for file upload
                            const formData = new FormData(e);
                            const userId = e.action.split("/").pop();
                            axios
                                .post(e.action, formData, {
                                    headers: {
                                        "X-CSRF-TOKEN":
                                            document.querySelector(
                                                "[name=_token]"
                                            ).value,
                                        "Content-Type": "multipart/form-data",
                                        "X-HTTP-Method-Override": "PUT"
                                    },
                                })
                                .then(function (response) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    Swal.fire({
                                        text: "User has been successfully updated!",
                                        icon: "success",
                                        buttonsStyling: !1,
                                        confirmButtonText:
                                            "Ok, got it!",
                                        customClass: {
                                            confirmButton:
                                                "btn btn-primary",
                                        },
                                    }).then(function (t) {
                                        window.location.href = "/users";
                                    });
                                })
                                .catch(function (error) {
                                    i.removeAttribute("data-kt-indicator");
                                    i.disabled = !1;
                                    if (
                                        error.response &&
                                        error.response.status === 422
                                    ) {
                                        let errors =
                                            error.response.data.errors;
                                        let errorList = "<ul>";
                                        for (let key in errors) {
                                            if (errors.hasOwnProperty(key)) {
                                                errors[key].forEach((msg) => {
                                                    errorList += `<li>${msg}</li>`;
                                                });
                                            }
                                        }
                                        errorList += "</ul>";
                                        Swal.fire({
                                            html: errorList,
                                            icon: "error",
                                            buttonsStyling: !1,
                                            confirmButtonText:
                                                "Ok, got it!",
                                            customClass: {
                                                confirmButton:
                                                    "btn btn-primary",
                                            },
                                        });
                                    }
                                });
                        } else {
                            Swal.fire({
                                text: "Sorry, looks like there are some errors detected, please try again.",
                                icon: "error",
                                buttonsStyling: !1,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary",
                                },
                            });
                        }
                    });
                });
            })();
        },
    };
})();
KTUtil.onDOMContentLoaded(function () {
    KTUsersUpdateUser.init();
});

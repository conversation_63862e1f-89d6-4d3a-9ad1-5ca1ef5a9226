"use strict";
var KTMaintenanceUpdateForm = function() {
    var section, form, validation, dropdownReset, resetBtn, submitBtn, fileInput, previewContainer, selectedFiles = [];
    return {
        init: function () {
            section = document.querySelector("#kt_maintenance_update");
            if (!section) return;
            form = section.querySelector("#kt_maintenance_add_update_form");
            if (!form) return;
            dropdownReset = form.querySelector('[data-kt-maintenance-update-action="forward-to-reset"]');
            resetBtn = form.querySelector('[data-kt-maintenance-update-action="reset"]');
            submitBtn = form.querySelector('[data-kt-maintenance-update-action="submit"]');
            fileInput = form.querySelector('input[type="file"][name="supporting_doc_url[]"]');
            previewContainer = form.querySelector('[id="update-supporting-docs-preview"]');

            const actionSelect = form.querySelector('select[name="action_id"]');
            const forwardToSelect = form.querySelector('select[name="forward_to"]');
            const workflowIdInput = form.querySelector('input[name="workflow_id"]');

            if (actionSelect && forwardToSelect && workflowIdInput) {
                // Handle action selection change (for Select2)
                $(actionSelect).on('select2:select', function (e) {
                    const selectedData = e.params.data;
                    const selectedOption = actionSelect.querySelector(`option[value="${selectedData.id}"]`);
                    const actionStatus = selectedOption ? selectedOption.getAttribute('data-kt-action-status') : null;
                    const currentWorkflowId = parseInt(workflowIdInput.value);

                    if (actionStatus && currentWorkflowId) {
                        this.fetchWorkflowActors(actionStatus, currentWorkflowId, forwardToSelect, workflowIdInput);
                    }
                }.bind(this));
            } else {
                this.errorAlert('One or more required elements not found');
            }

            validation = FormValidation.formValidation(form, {
                fields: {
                    action_id: {
                        validators: {
                            notEmpty: {
                                message: "Action is required"
                            }
                        }
                    },
                    comments: {
                        validators: {
                            notEmpty: {
                                message: "Comment/Note is required"
                            }
                        }
                    },
                },
                plugins: {
                    trigger: new FormValidation.plugins.Trigger,
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: ".fv-row",
                        eleInvalidClass: "",
                        eleValidClass: ""
                    })
                }
            });
            dropdownReset.addEventListener("click", (t) => {
                const forwardToSelect = form.querySelector('[name="forward_to"]');
                if (forwardToSelect) {
                    forwardToSelect.value = '';
                    $(forwardToSelect).trigger('change');
                }
            });
            resetBtn.addEventListener("click", (t) => {
                t.preventDefault();
                Swal.fire({
                    text: "Are you sure you would like to reset?",
                    icon: "warning",
                    showCancelButton: !0,
                    buttonsStyling: !1,
                    confirmButtonText: "Yes, reset it!",
                    cancelButtonText: "No, return",
                    customClass: {
                        confirmButton: "btn btn-primary",
                        cancelButton: "btn btn-active-light",
                    },
                }).then(function (t) {
                    t.value
                        ? (form.reset(), window.location.reload())
                        : "cancel" === t.dismiss &&
                        Swal.fire({
                            text: "Your form has not been reset!.",
                            icon: "error",
                            buttonsStyling: !1,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-primary",
                            },
                        });
                });
            });
            // --- File input and preview logic ---
            if (fileInput) {
                fileInput.addEventListener('change', function(ev) {
                    for (var j = 0; j < ev.target.files.length; j++) {
                        selectedFiles.push(ev.target.files[j]);
                    }
                    renderPreviews();
                    fileInput.value = '';
                });
            }
            function renderPreviews() {
                if (!previewContainer) return;
                // Only render new files (existing handled by blade)
                // Remove all new file previews first
                var newFileDivs = previewContainer.querySelectorAll('.new-file-preview');
                newFileDivs.forEach(function(div) { div.remove(); });
                selectedFiles.forEach(function(file, idx) {
                    var url = URL.createObjectURL(file);
                    var wrapper = document.createElement('div');
                    wrapper.className = 'position-relative m-1 shadow-sm bg-white rounded d-flex flex-column align-items-center justify-content-center new-file-preview';
                    wrapper.style.width = '90px';
                    wrapper.style.minHeight = '90px';
                    wrapper.style.border = '1px solid #e5e5e5';
                    wrapper.style.overflow = 'hidden';
                    wrapper.style.flex = '0 0 auto';
                    var ext = file.name.split('.').pop().toLowerCase();
                    var isImage = file.type.indexOf('image/') === 0;
                    var content;
                    if (isImage) {
                        content = '<img src="' + url + '" style="width:88px;height:88px;object-fit:cover;border-radius:6px;" />';
                    } else {
                        content = '<div class="d-flex flex-column align-items-center justify-content-center" style="width:88px;height:88px;">' +
                            '<i class="ki-outline ki-file fs-2"></i>' +
                            '<span class="fs-8">' + ext.toUpperCase() + '</span>' +
                            '</div>';
                    }
                    wrapper.innerHTML =
                        content +
                        '<button type="button" class="btn btn-xs btn-danger position-absolute top-0 end-0 m-1 px-1 py-0" style="border-radius:50%;width:22px;height:22px;line-height:1;z-index:2;" data-remove-idx="' + idx + '">' +
                        '<i class="ki-outline ki-cross fs-6"></i>' +
                        '</button>';
                    previewContainer.appendChild(wrapper);
                });
                previewContainer.style.display = 'flex';
                previewContainer.style.flexWrap = 'wrap';
                previewContainer.style.gap = '12px';
            }
            if (previewContainer) {
                previewContainer.addEventListener('click', function(e) {
                    var btn = e.target.closest('button[data-remove-idx]');
                    if (btn) {
                        var idx = parseInt(btn.getAttribute('data-remove-idx'));
                        selectedFiles.splice(idx, 1);
                        renderPreviews();
                    }
                });
            }
            // --- End file preview logic ---
            // Submit logic
            submitBtn.addEventListener("click", function(e) {
                e.preventDefault();
                validation && validation.validate().then((function (e) {
                    if ("Valid" == e) {
                        submitBtn.setAttribute("data-kt-indicator", "on");
                        submitBtn.disabled = !0;
                        const formData = new FormData(form);
                        // Remove any existing files in FormData
                        formData.delete('supporting_doc_url[]');
                        selectedFiles.forEach(function (file) {
                            formData.append('supporting_doc_url[]', file);
                        });
                        const internal = document.querySelector("[name=updatable_id]").value;
                        axios.post(`/estate/maintenance/internal/${internal}/add-update`, formData, {
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('[name=_token]').value,
                                'Content-Type': 'multipart/form-data',
                                "X-HTTP-Method-Override": "PUT"
                            }
                        })
                            .then(function (response) {
                                submitBtn.removeAttribute("data-kt-indicator");
                                submitBtn.disabled = false;
                                Swal.fire({
                                    text: 'Update submitted successfully!',
                                    icon: 'success',
                                    buttonsStyling: false,
                                    confirmButtonText: 'Ok, got it!',
                                    customClass: { confirmButton: 'btn btn-primary' }
                                }).then(function (t) {
                                    if (t.isConfirmed) window.location.reload();
                                });
                            })
                            .catch(function (error) {
                                submitBtn.removeAttribute("data-kt-indicator");
                                submitBtn.disabled = false;
                                if (error.response && error.response.status === 422) {
                                    let errors = error.response.data.errors;
                                    let errorList = '<ul>';
                                    for (let key in errors) {
                                        if (errors.hasOwnProperty(key)) {
                                            errors[key].forEach((msg) => {
                                                errorList += `<li>${msg}</li>`;
                                            });
                                        }
                                    }
                                    errorList += '</ul>';
                                    Swal.fire({
                                        html: errorList,
                                        icon: "error",
                                        confirmButtonText: "Ok, got it!",
                                        customClass: {
                                            confirmButton: "btn btn-danger"
                                        }
                                    });
                                }
                            });
                    } else {
                        Swal.fire({
                            html: "Sorry, looks like there are some errors detected, please try again.",
                            icon: "error",
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-danger"
                            }
                        });
                    }
                }))
            });
        },
        errorAlert: function(message = 'An error occurred. Please try again.', format = 'text') {
            if (format === 'html') {
                Swal.fire({
                    html: message,
                    icon: "error",
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-danger"
                    }
                });
            } else {
                Swal.fire({
                    text: message,
                    icon: "error",
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-danger"
                    }
                });
            }
        },
        fetchWorkflowActors: function(actionStatus, currentWorkflowId, forwardToSelect, workflowIdInput) {
            // Get current workflow details from the page
            const maintenanceData = this.getMaintenanceWorkflowData(workflowIdInput);

            if (!maintenanceData) {
                this.errorAlert('Could not retrieve maintenance workflow data in order to update the "Escalate/de-escalate to" field. Please try again.');
                return;
            }

            // Determine target workflow based on action status
            switch(actionStatus) {
                case 'proceed':
                    // Get next workflow stage
                    this.getWorkflowByStage(maintenanceData.menuId, maintenanceData.currentStage + 1)
                        .then(workflow => {
                            if (workflow) {
                                this.fetchActors(workflow.id, forwardToSelect);
                            } else {
                                this.errorAlert('No next stage available');
                            }
                        });
                    break;

                case 'rollback':
                    // Get previous workflow stage
                    if (maintenanceData.currentStage > 1) {
                        this.getWorkflowByStage(maintenanceData.menuId, maintenanceData.currentStage - 1)
                            .then(workflow => {
                                if (workflow) {
                                    this.fetchActors(workflow.id, forwardToSelect);
                                } else {
                                    this.errorAlert('No previous stage available');
                                }
                            });
                    } else {
                        this.errorAlert('Cannot rollback from first stage');
                    }
                    break;

                case 'freeze':
                    // Use current workflow
                    this.fetchActors(currentWorkflowId, forwardToSelect);
                    break;

                default:
                    // For other actions, use current workflow
                    this.fetchActors(currentWorkflowId, forwardToSelect);
                    break;
            }
        },

        getMaintenanceWorkflowData: function(workflowIdInput) {
            if (!workflowIdInput) return null;

            const menuId = parseInt(workflowIdInput.getAttribute('data-menu-id'));
            const currentStage = parseInt(workflowIdInput.getAttribute('data-current-stage'));
            const workflowId = parseInt(workflowIdInput.value);

            if (!menuId || !currentStage || !workflowId) {
                console.error('Missing workflow data attributes');
                return null;
            }

            return {
                menuId: menuId,
                currentStage: currentStage,
                workflowId: workflowId
            };
        },

        getWorkflowByStage: function(menuId, stageNumber) {
            // Create a simple API call to get workflow by menu and stage
            const formData = new FormData();
            formData.append('menu_id', menuId);
            formData.append('stage_number', stageNumber);

            return axios.post('/workflows/get-by-stage', formData, {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('[name=_token]').value,
                    'Content-Type': 'multipart/form-data'
                }
            })
            .then(response => response.data)
            .catch(error => {
                this.errorAlert('Error fetching workflow by stage');
                return null;
            });
        },

        fetchActors: function(workflowId, forwardToSelect) {
            const formData = new FormData();
            formData.append('workflow_id', workflowId);

            axios.post('/workflows/get-actors', formData, {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('[name=_token]').value,
                    'Content-Type': 'multipart/form-data'
                }
            })
            .then(response => {
                this.populateForwardToSelect(forwardToSelect, response.data.data);
            })
            .catch(error => {
                this.errorAlert('Error loading actors');
            });
        },

        populateForwardToSelect: function(forwardToSelect, actors) {
            // Clear existing options
            forwardToSelect.innerHTML = '<option value="">Select ...</option>';

            console.log('actors', actors);

            // Add new options
            if (actors && actors.length > 0) {
                actors.forEach(actor => {
                    const option = document.createElement('option');
                    option.value = actor.id;
                    option.textContent = `${actor.rank?.code || 'N/A'} (${actor.fullname || actor.name})`;
                    forwardToSelect.appendChild(option);
                });
            }

            // Trigger Select2 update if it's initialized
            if ($(forwardToSelect).hasClass('select2-hidden-accessible')) {
                $(forwardToSelect).trigger('change');
            }
        },
    };
}();
KTUtil.onDOMContentLoaded(function() {
    KTMaintenanceUpdateForm.init();
});

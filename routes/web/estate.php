<?php

use App\Http\Controllers\Estate\Estate\DepartmentController;
use App\Http\Controllers\Estate\Estate\EstateActionController;
use App\Http\Controllers\Estate\Estate\EstateController;
use App\Http\Controllers\Estate\Estate\EstateEncumbrencesController;
use App\Http\Controllers\Estate\Estate\EstateExternalMaintenanceController;
use App\Http\Controllers\Estate\Estate\EstateInternalMaintenanceController;
use App\Http\Controllers\Estate\ServiceProviders\AgentController;
use App\Http\Controllers\Estate\Estate\WorkTypeController;
use Illuminate\Support\Facades\Route;

Route::resource('departments', DepartmentController::class)
    ->only(['index', 'store', 'edit', 'update', 'destroy']);
Route::resource('work-types', WorkTypeController::class)
    ->only(['index', 'store', 'edit', 'update', 'destroy']);
Route::resource('estate-actions', EstateActionController::class)
    ->only(['index', 'store', 'edit', 'update', 'destroy']);

Route::resource('dashboard', EstateController::class)
    ->only(['index'])
    ->name('index', 'estate.dashboard');

Route::resource('maintenance/internal', EstateInternalMaintenanceController::class)
    ->only(['index', 'store', 'show', 'edit', 'update'])
    ->names([
        'index' => 'maintenance.internal.index',
        'store' => 'maintenance.internal.store',
        'show' => 'maintenance.internal.show',
        'edit' => 'maintenance.internal.edit',
        'update' => 'maintenance.internal.update',
    ]);
Route::put('maintenance/internal/{internal}/add-update', [
        EstateInternalMaintenanceController::class, 'addUpdate'
    ])->name('maintenance.internal.add-update');
Route::post('maintenance/internal/delete-supporting-doc', [
        EstateInternalMaintenanceController::class, 'deleteSupportingDoc'
    ])->name('maintenance.internal.delete-supporting-doc');

Route::resource('maintenance/external', EstateExternalMaintenanceController::class)
    ->only(['index', 'store', 'show', 'edit', 'update'])
    ->names([
        'index' => 'maintenance.external.index',
        'store' => 'maintenance.external.store',
        'show' => 'maintenance.external.show',
        'edit' => 'maintenance.external.edit',
        'update' => 'maintenance.external.update',
    ]);
Route::put('maintenance/external/{external}/add-update', [
        EstateExternalMaintenanceController::class, 'addUpdate'
    ])->name('maintenance.external.add-update');
Route::post('maintenance/external/delete-supporting-doc', [
        EstateExternalMaintenanceController::class, 'deleteSupportingDoc'
    ])->name('maintenance.external.delete-supporting-doc');

Route::resource('encumbrences', EstateEncumbrencesController::class)
    ->only(['index', 'store', 'show', 'update']);

Route::apiResource('agents', AgentController::class);

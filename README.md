### Sync Local with Remote
git checkout main && git pull && git remote prune origin && git fetch -p && for branch in $(git branch -vv | grep ": gone]" | awk "{print \$1}"); do git branch -D "$branch"; done;

### Theme Documentation
https://preview.keenthemes.com/html/metronic/docs/forms/formvalidation/overview

### Laravel IDE Helper
## This package generates helper files that make Intelephense and other IDEs recognize <PERSON><PERSON>’s magic methods and properties.
composer require --dev barryvdh/laravel-ide-helper
php artisan ide-helper:models

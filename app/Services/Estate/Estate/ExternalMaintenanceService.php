<?php

namespace App\Services\Estate\Estate;

use App\Events\ExternalMaintenanceAssigned;
use App\Events\ExternalMaintenanceCompleted;
use App\Models\Estate\Estate\ExternalMaintenance;
use App\Models\Estate\ServiceProviders\Agent;
use App\Repositories\Estate\Estate\ExternalMaintenanceRepository;
use App\Services\Estate\Estate\EstateUpdateService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ExternalMaintenanceService
{
    public function __construct(
        protected ExternalMaintenanceRepository $repository,
        protected EstateUpdateService $updateService
    ) {
    }

    /**
     * Create a new external maintenance record.
     */
    public function createMaintenance(array $data): ExternalMaintenance
    {
        try {
            DB::beginTransaction();

            $data['created_by'] = auth()->id();
            $data['modified_by'] = auth()->id();
            $data['status'] = $data['status'] ?? 'pending';
            $data['type'] = 'external';

            $maintenance = $this->repository->create($data);

            // Create initial update record
            $this->updateService->externalMaintenanceCreated($maintenance);

            DB::commit();

            Log::info('External maintenance created successfully', [
                'maintenance_id' => $maintenance->id,
                'location' => $maintenance->location,
                'agent_id' => $maintenance->requesting_agent_id,
                'created_by' => auth()->id()
            ]);

            return $maintenance;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create external maintenance', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Update an existing external maintenance record.
     */
    public function updateMaintenance(ExternalMaintenance $maintenance, array $data): ExternalMaintenance
    {
        try {
            DB::beginTransaction();

            $originalStatus = $maintenance->status;
            $data['modified_by'] = auth()->id();

            $updatedMaintenance = $this->repository->update($maintenance, $data);

            // Handle status changes
            if (isset($data['status']) && $data['status'] !== $originalStatus) {
                $this->handleStatusChange($updatedMaintenance, $originalStatus, $data['status']);
            }

            DB::commit();

            Log::info('External maintenance updated successfully', [
                'maintenance_id' => $maintenance->id,
                'location' => $maintenance->location,
                'status_change' => $originalStatus . ' -> ' . ($data['status'] ?? $originalStatus),
                'updated_by' => auth()->id()
            ]);

            return $updatedMaintenance;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update external maintenance', [
                'maintenance_id' => $maintenance->id,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Delete an external maintenance record.
     */
    public function deleteMaintenance(ExternalMaintenance $maintenance): bool
    {
        try {
            DB::beginTransaction();

            // Check if maintenance can be deleted
            if ($maintenance->isInProgress()) {
                throw new \Exception('Cannot delete maintenance that is in progress');
            }

            $result = $this->repository->delete($maintenance);

            DB::commit();

            Log::info('External maintenance deleted successfully', [
                'maintenance_id' => $maintenance->id,
                'location' => $maintenance->location,
                'deleted_by' => auth()->id()
            ]);

            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete external maintenance', [
                'maintenance_id' => $maintenance->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Assign maintenance to an agent.
     */
    public function assignToAgent(ExternalMaintenance $maintenance, int $agentId): ExternalMaintenance
    {
        try {
            DB::beginTransaction();

            $agent = Agent::findOrFail($agentId);

            $updatedMaintenance = $this->updateMaintenance($maintenance, [
                'requesting_agent_id' => $agentId,
                'status' => 'assigned'
            ]);

            // Fire assignment event
            event(new ExternalMaintenanceAssigned($updatedMaintenance, $agent));

            DB::commit();

            Log::info('External maintenance assigned to agent', [
                'maintenance_id' => $maintenance->id,
                'agent_id' => $agentId,
                'agent_name' => $agent->display_name,
                'assigned_by' => auth()->id()
            ]);

            return $updatedMaintenance;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to assign maintenance to agent', [
                'maintenance_id' => $maintenance->id,
                'agent_id' => $agentId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Mark maintenance as completed.
     */
    public function markAsCompleted(ExternalMaintenance $maintenance, string $completionNotes = null): ExternalMaintenance
    {
        try {
            DB::beginTransaction();

            $updatedMaintenance = $this->updateMaintenance($maintenance, [
                'status' => 'completed'
            ]);

            $agent = $maintenance->requestingAgent;

            // Fire completion event
            if ($agent) {
                event(new ExternalMaintenanceCompleted($updatedMaintenance, $agent, auth()->user(), $completionNotes));
            }

            DB::commit();

            Log::info('External maintenance marked as completed', [
                'maintenance_id' => $maintenance->id,
                'agent_id' => $maintenance->requesting_agent_id,
                'completed_by' => auth()->id(),
                'completion_notes' => $completionNotes
            ]);

            return $updatedMaintenance;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to mark maintenance as completed', [
                'maintenance_id' => $maintenance->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get all maintenance records.
     */
    public function getAllMaintenances(): Collection
    {
        return $this->repository->getAllMaintenances();
    }

    /**
     * Get paginated maintenance records.
     */
    public function getPaginatedMaintenances(int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->getPaginatedMaintenances($perPage);
    }

    /**
     * Get maintenance by ID.
     */
    public function getMaintenanceById(int $id): ?ExternalMaintenance
    {
        return $this->repository->getMaintenanceById($id);
    }

    /**
     * Get maintenance records by status.
     */
    public function getMaintenancesByStatus(string $status): Collection
    {
        return $this->repository->getMaintenancesByStatus($status);
    }

    /**
     * Get maintenance records by agent.
     */
    public function getMaintenancesByAgent(int $agentId): Collection
    {
        return $this->repository->getMaintenancesByAgent($agentId);
    }

    /**
     * Get maintenance records by department.
     */
    public function getMaintenancesByDepartment(int $departmentId): Collection
    {
        return $this->repository->getMaintenancesByDepartment($departmentId);
    }

    /**
     * Search maintenance records.
     */
    public function searchMaintenances(string $query): Collection
    {
        return $this->repository->searchMaintenances($query);
    }

    /**
     * Get maintenance statistics.
     */
    public function getMaintenanceStatistics(): array
    {
        return $this->repository->getMaintenanceStatistics();
    }

    /**
     * Handle status changes.
     */
    private function handleStatusChange(ExternalMaintenance $maintenance, string $oldStatus, string $newStatus): void
    {
        // Create update record for status change
        $this->updateService->externalMaintenanceStatusChanged($maintenance, $oldStatus, $newStatus);

        // Additional logic based on status change
        switch ($newStatus) {
            case 'in_progress':
                Log::info('External maintenance started', [
                    'maintenance_id' => $maintenance->id,
                    'agent_id' => $maintenance->requesting_agent_id
                ]);
                break;

            case 'completed':
                Log::info('External maintenance completed', [
                    'maintenance_id' => $maintenance->id,
                    'agent_id' => $maintenance->requesting_agent_id
                ]);
                break;

            case 'cancelled':
                Log::info('External maintenance cancelled', [
                    'maintenance_id' => $maintenance->id,
                    'agent_id' => $maintenance->requesting_agent_id
                ]);
                break;
        }
    }

    /**
     * Validate maintenance data.
     */
    public function validateMaintenanceData(array $data): array
    {
        $errors = [];

        // Required fields
        if (empty($data['location'])) {
            $errors[] = 'Location is required';
        }

        if (empty($data['description'])) {
            $errors[] = 'Description is required';
        }

        if (empty($data['department_id'])) {
            $errors[] = 'Department is required';
        }

        if (empty($data['work_type_id'])) {
            $errors[] = 'Work type is required';
        }

        // Validate cost implication
        if (isset($data['cost_implication']) && $data['cost_implication'] < 0) {
            $errors[] = 'Cost implication cannot be negative';
        }

        // Validate entry date
        if (isset($data['entry_date']) && strtotime($data['entry_date']) > time()) {
            $errors[] = 'Entry date cannot be in the future';
        }

        return $errors;
    }
}

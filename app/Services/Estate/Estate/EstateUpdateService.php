<?php

namespace App\Services\Estate\Estate;

use App\Events\MaintenanceEvent;
use App\Models\Estate\Estate\EstateAction;
use App\Models\Estate\Estate\EstateUpdate;
use App\Models\Estate\Estate\Maintenance;
use App\Repositories\Estate\Estate\EstateUpdateRepository;
use App\Services\Estate\Estate\EstateActionService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class EstateUpdateService
{
    public function __construct(
        protected EstateActionService $actionService,
        protected EstateUpdateRepository $repo
    ) {
    }

    public function maintenanceCreated(Maintenance $maintenance): void
    {
        $currentUser = auth()->user();
        $action = $this->actionService->getEstateActionByStatus('created');

        /** @var EstateUpdate $update */
        $update = $maintenance->updates()->create([
            'action_id' => $action->id,
            'comments' => 'New maintenance entry created',
            'created_by' => $currentUser->id,
            'modified_by' => $currentUser->id,
        ]);

        $this->triggerNotificationEvent($maintenance, $action, $update);
    }

    /**
     * Handle external maintenance creation.
     */
    public function externalMaintenanceCreated(\App\Models\Estate\Estate\ExternalMaintenance $maintenance): void
    {
        $currentUser = auth()->user();
        $action = $this->actionService->getEstateActionByStatus('created');

        /** @var EstateUpdate $update */
        $update = $maintenance->updates()->create([
            'action_id' => $action->id,
            'comments' => 'New external maintenance entry created',
            'created_by' => $currentUser->id,
            'modified_by' => $currentUser->id,
        ]);

        // Note: External maintenance uses different event handling
        // The notification will be handled by ExternalMaintenanceAssigned event
    }

    /**
     * Handle external maintenance status change.
     */
    public function externalMaintenanceStatusChanged(\App\Models\Estate\Estate\ExternalMaintenance $maintenance, string $oldStatus, string $newStatus): void
    {
        $currentUser = auth()->user();
        $action = $this->actionService->getEstateActionByStatus('updated');

        /** @var EstateUpdate $update */
        $update = $maintenance->updates()->create([
            'action_id' => $action->id,
            'comments' => "Status changed from {$oldStatus} to {$newStatus}",
            'created_by' => $currentUser->id,
            'modified_by' => $currentUser->id,
        ]);
    }

    public function maintenanceEdited(Maintenance $maintenance): void
    {
        $currentUser = auth()->user();
        $action = $this->actionService->getEstateActionByStatus('edited');

        /** @var EstateUpdate $update */
        $update = $maintenance->updates()->create([
            'action_id' => $action->id,
            'comments' => 'Maintenance entry edited',
            'created_by' => $currentUser->id,
            'modified_by' => $currentUser->id,
        ]);

        $this->triggerNotificationEvent($maintenance, $action, $update);
    }

    public function applyUpdateToMaintenance(Maintenance $maintenance, array $data): EstateUpdate
    {
        $currentUser = auth()->user();
        $action = $this->actionService->getEstateActionById($data['action_id']);

        /** @var EstateUpdate $update */
        $update = $maintenance->updates()->create([
            ...$data,
            'created_by' => $currentUser->id,
            'modified_by' => $currentUser->id,
        ]);

        $this->triggerNotificationEvent($maintenance, $action, $update);

        return $update;
    }

    public function triggerNotificationEvent(Maintenance $maintenance, EstateAction $action, EstateUpdate $update): void
    {
        MaintenanceEvent::dispatch($maintenance, $action, $update);
    }
}

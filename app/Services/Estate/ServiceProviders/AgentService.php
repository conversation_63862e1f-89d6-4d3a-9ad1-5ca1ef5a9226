<?php

namespace App\Services\Estate\ServiceProviders;

use App\Events\AgentRegistered;
use App\Models\Estate\ServiceProviders\Agent;
use App\Repositories\Estate\ServiceProviders\AgentRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AgentService
{
    public function __construct(
        protected AgentRepository $repository
    ) {
    }

    /**
     * Create a new agent.
     */
    public function createAgent(array $data): Agent
    {
        try {
            DB::beginTransaction();

            $data['created_by'] = auth()->id();
            $data['modified_by'] = auth()->id();

            $agent = $this->repository->create($data);

            // Fire agent registered event
            event(new AgentRegistered($agent));

            DB::commit();

            Log::info('Agent created successfully', [
                'agent_id' => $agent->id,
                'agent_name' => $agent->display_name,
                'created_by' => auth()->id()
            ]);

            return $agent;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create agent', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Update an existing agent.
     */
    public function updateAgent(Agent $agent, array $data): Agent
    {
        try {
            DB::beginTransaction();

            $data['modified_by'] = auth()->id();
            $updatedAgent = $this->repository->update($agent, $data);

            DB::commit();

            Log::info('Agent updated successfully', [
                'agent_id' => $agent->id,
                'agent_name' => $agent->display_name,
                'updated_by' => auth()->id()
            ]);

            return $updatedAgent;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update agent', [
                'agent_id' => $agent->id,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Delete an agent.
     */
    public function deleteAgent(Agent $agent): bool
    {
        try {
            DB::beginTransaction();

            // Check if agent has active services or ongoing maintenance
            if ($agent->activeServices()->count() > 0) {
                throw new \Exception('Cannot delete agent with active services');
            }

            if ($agent->externalMaintenances()->whereIn('status', ['pending', 'in_progress', 'assigned'])->count() > 0) {
                throw new \Exception('Cannot delete agent with ongoing maintenance tasks');
            }

            $result = $this->repository->delete($agent);

            DB::commit();

            Log::info('Agent deleted successfully', [
                'agent_id' => $agent->id,
                'agent_name' => $agent->display_name,
                'deleted_by' => auth()->id()
            ]);

            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete agent', [
                'agent_id' => $agent->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get all agents.
     */
    public function getAllAgents(): Collection
    {
        return $this->repository->getAllAgents();
    }

    /**
     * Get paginated agents.
     */
    public function getPaginatedAgents(int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->getPaginatedAgents($perPage);
    }

    /**
     * Get agent by ID.
     */
    public function getAgentById(int $id): ?Agent
    {
        return $this->repository->getAgentById($id);
    }

    /**
     * Get corporate agents.
     */
    public function getCorporateAgents(): Collection
    {
        return $this->repository->getCorporateAgents();
    }

    /**
     * Get individual agents.
     */
    public function getIndividualAgents(): Collection
    {
        return $this->repository->getIndividualAgents();
    }

    /**
     * Search agents.
     */
    public function searchAgents(string $query): Collection
    {
        return $this->repository->searchAgents($query);
    }

    /**
     * Get agents by city.
     */
    public function getAgentsByCity(int $cityId): Collection
    {
        return $this->repository->getAgentsByCity($cityId);
    }

    /**
     * Get agents by tenant category.
     */
    public function getAgentsByTenantCategory(int $categoryId): Collection
    {
        return $this->repository->getAgentsByTenantCategory($categoryId);
    }

    /**
     * Get agents with active services.
     */
    public function getAgentsWithActiveServices(): Collection
    {
        return $this->repository->getAgentsWithActiveServices();
    }

    /**
     * Get agents by service category.
     */
    public function getAgentsByServiceCategory(string $category): Collection
    {
        return $this->repository->getAgentsByServiceCategory($category);
    }

    /**
     * Get agents by service type.
     */
    public function getAgentsByServiceType(string $type): Collection
    {
        return $this->repository->getAgentsByServiceType($type);
    }

    /**
     * Get agents with services in cost range.
     */
    public function getAgentsWithServicesInCostRange(float $minCost, float $maxCost): Collection
    {
        return $this->repository->getAgentsWithServicesInCostRange($minCost, $maxCost);
    }

    /**
     * Get agent statistics.
     */
    public function getAgentStatistics(): array
    {
        return $this->repository->getAgentStatistics();
    }

    /**
     * Validate agent data.
     */
    public function validateAgentData(array $data): array
    {
        $errors = [];

        // Check for required fields
        if (empty($data['fullname'])) {
            $errors[] = 'Full name is required';
        }

        if (empty($data['personal_email']) && empty($data['cooperate_email'])) {
            $errors[] = 'At least one email address is required';
        }

        if (empty($data['mobile_number'])) {
            $errors[] = 'Mobile number is required';
        }

        // Validate email formats
        if (!empty($data['personal_email']) && !filter_var($data['personal_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Personal email format is invalid';
        }

        if (!empty($data['cooperate_email']) && !filter_var($data['cooperate_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Corporate email format is invalid';
        }

        // Check for duplicate emails
        if (!empty($data['personal_email'])) {
            $existingAgent = Agent::where('personal_email', $data['personal_email'])->first();
            if ($existingAgent && (!isset($data['id']) || $existingAgent->id !== $data['id'])) {
                $errors[] = 'Personal email already exists';
            }
        }

        if (!empty($data['cooperate_email'])) {
            $existingAgent = Agent::where('cooperate_email', $data['cooperate_email'])->first();
            if ($existingAgent && (!isset($data['id']) || $existingAgent->id !== $data['id'])) {
                $errors[] = 'Corporate email already exists';
            }
        }

        return $errors;
    }

    /**
     * Get available agents for maintenance assignment.
     */
    public function getAvailableAgentsForMaintenance(string $serviceCategory = null, string $serviceType = null): Collection
    {
        $query = $this->repository->getAgentsWithActiveServices();

        if ($serviceCategory) {
            $query = $this->repository->getAgentsByServiceCategory($serviceCategory);
        }

        if ($serviceType) {
            $query = $this->repository->getAgentsByServiceType($serviceType);
        }

        return $query;
    }
}

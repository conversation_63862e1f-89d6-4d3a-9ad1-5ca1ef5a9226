<?php

namespace App\Models\Shared;

use App\Models\Shared\UserManagement\Permission;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $code
 * @property string $name
 * @property string|null $description
 * @property bool $is_active
 * @property int $module_id
 * @property int $created_by
 * @property int $modified_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Shared\Menu> $menus
 * @property-read int|null $menus_count
 * @property-read \App\Models\Shared\Module $module
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Permission> $permissions
 * @property-read int|null $permissions_count
 * @method static \Database\Factories\Shared\SubModuleFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereModifiedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereModuleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubModule whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SubModule extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'is_active',
        'code',
        'module_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'is_active' => 'boolean',
            'module_id' => 'integer',
        ];
    }

    public function module(): BelongsTo
    {
        return $this->belongsTo(Module::class);
    }

    public function permissions(): HasMany
    {
        return $this->hasMany(Permission::class);
    }

    public function menus(): HasMany
    {
        return $this->hasMany(Menu::class);
    }
}

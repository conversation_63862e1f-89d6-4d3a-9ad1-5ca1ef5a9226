<?php

namespace App\Models\Shared\UserManagement;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $name
 * @property string $code
 * @property string|null $description
 * @property bool $is_active
 * @property int $created_by
 * @property int $modified_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read User $createdBy
 * @property-read User $modifiedBy
 * @method static \Database\Factories\Shared\UserManagement\UserDesignationFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation whereModifiedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDesignation withoutTrashed()
 * @mixin \Eloquent
 */
class UserDesignation extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'is_active',
        'created_by',
        'modified_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'is_active' => 'boolean',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'created_by' => 'integer',
            'modified_by' => 'integer',
        ];
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'modified_by');
    }
}

<?php

namespace App\Models\Shared\UserManagement;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $name
 * @property string $code
 * @property string|null $description
 * @property bool $is_active
 * @property int $created_by
 * @property int $modified_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read User $createdBy
 * @property-read User $modifiedBy
 * @property-read \Illuminate\Database\Eloquent\Collection<int, User> $users
 * @property-read int|null $users_count
 * @method static \Database\Factories\Shared\UserManagement\UserRankFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank whereModifiedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank withTrashed(bool $withTrashed = true)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserRank withoutTrashed()
 * @mixin \Eloquent
 */
class UserRank extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'is_active',
        'created_by',
        'modified_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'is_active' => 'boolean',
            'created_by' => 'integer',
            'modified_by' => 'integer',
        ];
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'rank_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'modified_by');
    }
}

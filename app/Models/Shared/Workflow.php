<?php

namespace App\Models\Shared;

use App\Http\Resources\UserCollection;
use App\Http\Resources\UserRankCollection;
use App\Models\Shared\UserManagement\UserRank;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property string $action
 * @property int $stage_number
 * @property int $stage_progress_percent
 * @property bool $attachment_required
 * @property int $due_in_days
 * @property string $handlers
 * @property string $observers
 * @property bool $is_active
 * @property int $created_by
 * @property int $modified_by
 * @property int $menu_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read User|null $createdBy
 * @property-read \App\Models\Shared\Menu $menu
 * @property-read User|null $modifiedBy
 * @method static \Database\Factories\Shared\WorkflowFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereAttachmentRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereDueInDays($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereHandlers($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereMenuId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereModifiedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereObservers($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereStageNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereStageProgressPercent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Workflow whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Workflow extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'action',
        'stage_number',
        'stage_progress_percent',
        'attachment_required',
        'due_in_days',
        'handlers',
        'observers',
        'is_active',
        'created_by',
        'modified_by',
        'menu_id',
    ];

    /**PWor
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'stage_number' => 'integer',
            'stage_progress_percent' => 'integer',
            'attachment_required' => 'boolean',
            'is_active' => 'boolean',
            'created_by' => 'integer',
            'modified_by' => 'integer',
            'menu_id' => 'integer',
        ];
    }

    public function handlers(): UserRankCollection
    {
        $ids = explode(',', $this->handlers);
        $ranks = UserRank::whereIn('id', $ids)->get();
        return new UserRankCollection($ranks);
    }

    public function handlerUsers(): UserCollection
    {
        $ranks = $this->handlers()->pluck('id');
        $users = User::whereIn('rank_id', $ranks)->get();
        return new UserCollection($users);
    }

    public function observers(): UserRankCollection
    {
        $ids = explode(',', $this->observers);
        $ranks = UserRank::whereIn('id', $ids)->get();
        return new UserRankCollection($ranks);
    }

    public function observerUsers(): UserCollection
    {
        $ranks = $this->observers()->pluck('id');
        $users = User::whereIn('rank_id', $ranks)->get();
        return new UserCollection($users);
    }

    public function menu(): BelongsTo
    {
        return $this->belongsTo(Menu::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}

<?php

namespace App\Models\Estate\ServiceProviders;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Agent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'cooperate_name',
        'cooperate_address',
        'cooperate_telephone',
        'cooperate_email',
        'cooperate_registration_no',
        'cooperate_nature_of_business',
        'company_logo',
        'fullname',
        'gender',
        'religion',
        'marital_status',
        'personal_email',
        'residential_address',
        'city_id',
        'means_of_identification',
        'date_of_birth',
        'mobile_number',
        'tenant_category_id',
        'created_by',
        'modified_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'date_of_birth' => 'date',
            'created_by' => 'integer',
            'modified_by' => 'integer',
        ];
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * Get the services offered by this agent.
     */
    public function services(): HasMany
    {
        return $this->hasMany(AgentService::class);
    }

    /**
     * Get the active services offered by this agent.
     */
    public function activeServices(): HasMany
    {
        return $this->hasMany(AgentService::class)->where('is_active', true);
    }

    /**
     * Get external maintenance requests assigned to this agent.
     */
    public function externalMaintenances(): HasMany
    {
        return $this->hasMany(\App\Models\Estate\Estate\ExternalMaintenance::class, 'requesting_agent_id');
    }

    /**
     * Get the city this agent is located in.
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Shared\City::class);
    }

    /**
     * Get the tenant category this agent belongs to.
     */
    public function tenantCategory(): BelongsTo
    {
        return $this->belongsTo(\App\Models\TenantCategory::class);
    }

    /**
     * Get the full name of the agent.
     */
    public function getFullNameAttribute(): string
    {
        return $this->fullname;
    }

    /**
     * Get the company name or personal name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->cooperate_name ?: $this->fullname;
    }

    /**
     * Check if agent is a corporate entity.
     */
    public function isCorporate(): bool
    {
        return !empty($this->cooperate_name);
    }

    /**
     * Get primary contact email.
     */
    public function getPrimaryEmailAttribute(): string
    {
        return $this->cooperate_email ?: $this->personal_email;
    }

    /**
     * Scope to get corporate agents.
     */
    public function scopeCorporate($query)
    {
        return $query->whereNotNull('cooperate_name');
    }

    /**
     * Scope to get individual agents.
     */
    public function scopeIndividual($query)
    {
        return $query->whereNull('cooperate_name');
    }
}

<?php

namespace App\Models\Estate\Estate;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class ExternalMaintenance extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'type',
        'entry_date',
        'requesting_agent_id',
        'department_id',
        'location',
        'work_type_id',
        'other_work_type',
        'description',
        'proposed_action',
        'cost_implication',
        'checked_by',
        'supporting_doc_url',
        'notify_by_email',
        'notify_by_phone',
        'notify_in_app',
        'workflow_id',
        'status',
        'created_by',
        'modified_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'entry_date' => 'datetime',
            'department_id' => 'integer',
            'work_type_id' => 'integer',
            'cost_implication' => 'decimal',
            'checked_by' => 'integer',
            'notify_by_email' => 'boolean',
            'notify_by_phone' => 'boolean',
            'notify_in_app' => 'boolean',
            'workflow_id' => 'integer',
            'created_by' => 'integer',
            'modified_by' => 'integer',
        ];
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function workType(): BelongsTo
    {
        return $this->belongsTo(WorkType::class);
    }

    public function checkedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * Get the agent assigned to this external maintenance.
     */
    public function requestingAgent(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Estate\ServiceProviders\Agent::class, 'requesting_agent_id');
    }

    /**
     * Get the user who created this maintenance record.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the user who last modified this maintenance record.
     */
    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'modified_by');
    }

    /**
     * Get the workflow associated with this maintenance.
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Shared\Workflow::class);
    }

    /**
     * Get all updates for this maintenance (polymorphic relationship).
     */
    public function updates(): MorphMany
    {
        return $this->morphMany(\App\Models\Estate\Estate\EstateUpdate::class, 'updatable');
    }

    /**
     * Scope to get maintenance by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get maintenance by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get maintenance by agent.
     */
    public function scopeByAgent($query, int $agentId)
    {
        return $query->where('requesting_agent_id', $agentId);
    }

    /**
     * Check if maintenance is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if maintenance is in progress.
     */
    public function isInProgress(): bool
    {
        return in_array($this->status, ['in_progress', 'assigned']);
    }

    /**
     * Check if maintenance is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Shared\Workflow::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }
}

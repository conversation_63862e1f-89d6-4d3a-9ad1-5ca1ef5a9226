<?php

namespace App\Models\Estate\Estate;

use App\Models\Estate\Estate\EstateUpdate;
use App\Models\Shared\Workflow;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @property int $id
 * @property string $type
 * @property \Illuminate\Support\Carbon $entry_date
 * @property string $requesting_officer_name
 * @property int $department_id
 * @property string $location
 * @property int $work_type_id
 * @property string|null $other_work_type
 * @property string $description
 * @property string $proposed_action
 * @property float $cost_implication
 * @property int $checked_by
 * @property string $supporting_doc_url
 * @property bool $notify_by_email
 * @property bool $notify_by_phone
 * @property bool $notify_in_app
 * @property int $workflow_id
 * @property string $status
 * @property int $created_by
 * @property int $modified_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read User $checkedBy
 * @property-read User $createdBy
 * @property-read \App\Models\Estate\Estate\Department $department
 * @property-read User $modifiedBy
 * @property-read \Illuminate\Database\Eloquent\Collection<int, EstateUpdate> $updates
 * @property-read int|null $updates_count
 * @property-read \App\Models\Estate\Estate\WorkType $workType
 * @property-read Workflow $workflow
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereCheckedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereCostImplication($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereDepartmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereEntryDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereModifiedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereNotifyByEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereNotifyByPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereNotifyInApp($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereOtherWorkType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereProposedAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereRequestingOfficerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereSupportingDocUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereWorkTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Maintenance whereWorkflowId($value)
 * @mixin \Eloquent
 */
class Maintenance extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'type',
        'entry_date',
        'requesting_officer_name',
        'department_id',
        'location',
        'work_type_id',
        'other_work_type',
        'description',
        'proposed_action',
        'cost_implication',
        'checked_by',
        'supporting_doc_url',
        'notify_by_email',
        'notify_by_phone',
        'notify_in_app',
        'workflow_id',
        'status',
        'created_by',
        'modified_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'entry_date' => 'date',
            'department_id' => 'integer',
            'work_type_id' => 'integer',
            'cost_implication' => 'float',
            'checked_by' => 'integer',
            'notify_by_email' => 'boolean',
            'notify_by_phone' => 'boolean',
            'notify_in_app' => 'boolean',
            'workflow_id' => 'integer',
            'created_by' => 'integer',
            'modified_by' => 'integer',
            'supporting_doc_url' => 'string',
        ];
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function workType(): BelongsTo
    {
        return $this->belongsTo(WorkType::class);
    }

    public function checkedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'checked_by');
    }

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'modified_by');
    }

    public function updates(): MorphMany
    {
        return $this->morphMany(EstateUpdate::class, 'updatable')->chaperone();
    }
}

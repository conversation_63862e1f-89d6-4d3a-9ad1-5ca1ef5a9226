<?php

namespace App\Models\Estate\Estate;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property string $name
 * @property string $color
 * @property string $status
 * @property string|null $description
 * @property bool $is_active
 * @property int $created_by
 * @property int $modified_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $createdBy
 * @property-read \App\Models\User|null $modifiedBy
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction whereModifiedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateAction whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class EstateAction extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'color',
        'status',
        'description',
        'is_active',
        'created_by',
        'modified_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'is_active' => 'boolean',
            'created_by' => 'integer',
            'modified_by' => 'integer',
        ];
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }
}

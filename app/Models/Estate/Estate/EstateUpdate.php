<?php

namespace App\Models\Estate\Estate;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property int $action_id
 * @property string|null $comments
 * @property string|null $supporting_doc_url
 * @property int|null $forward_to
 * @property int $updatable_id
 * @property string $updatable_type
 * @property int $created_by
 * @property int $modified_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Estate\Estate\EstateAction $action
 * @property-read User $createdBy
 * @property-read User|null $forwardTo
 * @property-read User $modifiedBy
 * @property-read Model|\Eloquent $updatable
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereActionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereForwardTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereModifiedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereSupportingDocUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereUpdatableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereUpdatableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateUpdate whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class EstateUpdate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'action_id',
        'comments',
        'supporting_doc_url',
        'forward_to',
        'created_by',
        'modified_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'id' => 'integer',
            'action_id' => 'integer',
            'forward_to' => 'integer',
            'created_by' => 'integer',
            'modified_by' => 'integer',
        ];
    }

    /**
     * Get the parent imageable model (Maintenace or Encumbrence).
     */
    public function updatable(): MorphTo
    {
        return $this->morphTo();
    }


    public function action(): BelongsTo
    {
        return $this->belongsTo(EstateAction::class);
    }

    public function forwardTo(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'forward_to');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function modifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'modified_by');
    }
}

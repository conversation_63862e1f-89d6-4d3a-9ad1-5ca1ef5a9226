<?php

namespace App\Providers;

use App\Events\UserRegistered;
use App\Events\MaintenanceEvent;
use App\Events\AgentRegistered;
use App\Events\ExternalMaintenanceAssigned;
use App\Events\ExternalMaintenanceCompleted;
use App\Listeners\MaintenanceEventNotification;
use App\Listeners\SendNewUserCredentialsMail;
use App\Listeners\SendUserNotification;
use App\Listeners\SendWelcomeNotification;
use App\Listeners\SendAgentWelcomeNotification;
use App\Listeners\NotifyAdminsOfNewAgent;
use App\Listeners\NotifyStakeholdersOfMaintenanceAssignment;
use App\Listeners\NotifyStakeholdersOfMaintenanceCompletion;
use Illuminate\Support\ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }
    protected $listen = [
        UserRegistered::class => [
            SendUserNotification::class,
            SendWelcomeNotification::class,
        ],
        UserRegisteredSecure::class => [
            SendNewUserCredentialsMail::class,
        ],
        MaintenanceEvent::class => [
            MaintenanceEventNotification::class,
        ],
        AgentRegistered::class => [
            SendAgentWelcomeNotification::class,
            NotifyAdminsOfNewAgent::class,
        ],
        ExternalMaintenanceAssigned::class => [
            NotifyStakeholdersOfMaintenanceAssignment::class,
        ],
        ExternalMaintenanceCompleted::class => [
            NotifyStakeholdersOfMaintenanceCompletion::class,
        ],
    ];
    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}

<?php

namespace App\Http\Controllers\Estate\Estate;

use App\Http\Controllers\Controller;
use App\Http\Requests\Estate\Estate\EstateUpdateStoreRequest;
use App\Http\Requests\Estate\Estate\MaintenanceStoreRequest;
use App\Http\Requests\Estate\Estate\MaintenanceUpdateRequest;
use App\Models\User;
use App\Models\Estate\Estate\Department;
use App\Models\Estate\Estate\Maintenance;
use App\Models\Estate\Estate\WorkType;
use App\Models\Shared\Menu;
use App\Services\Estate\Estate\EstateActionService;
use App\Services\Estate\Estate\EstateUpdateService;
use App\Services\Estate\Estate\MaintenanceService;
use App\Services\Shared\WorkflowService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\Response;

class EstateExternalMaintenanceController extends Controller
{
    public const STORAGE_PATH = 'z_files/documents/external-maintenance/';
    protected const MENU_NAME = 'External Maintenance';
    protected ?Menu $menu = null;

    public function __construct(
        protected EstateActionService $actionService,
        protected EstateUpdateService $updateService,
        protected MaintenanceService $service,
        protected WorkflowService $workflowService
    ) {
        $this->middleware('can:read.external-maintenance');
        $this->menu = Menu::where('title', self::MENU_NAME)->first();
    }

    public function index(Request $request): RedirectResponse|View
    {
        $stageOneWorkflow = $this->workflowService->getWorkflowByMenuAndStage($this->menu->id, 1);

        if (!$stageOneWorkflow) {
            $pageName = self::MENU_NAME;
            $request->session()->flash(
                'warning',
                "A workflow for stage 1 of {$pageName} does not exist. Please create one."
            );
            return redirect()->route('workflows.index');
        }

        return view('estate.estate.maintenance.external-maintenance.index', [
            'maintenances' => Maintenance::where('type', 'external')->get(),
            'departments' => Department::where('is_active', true)->get(),
            'workTypes' => WorkType::where('is_active', true)->get(),
            'users' => User::all(),
            'workflow' => $stageOneWorkflow,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(MaintenanceStoreRequest $request): Response|ResponseFactory
    {
        if (auth()->user()->cannot('create.external-maintenance')) {
            abort(403);
        }

        $workflow = $this->workflowService->getWorkflowById($request->workflow_id);

        if ($workflow->attachment_required && !$request->hasFile('supporting_doc_url')) {
            return $this->error(
                'Validation Error',
                422,
                ['supporting_doc_url' => ['Supporting documents are required.']],
            );
        }

        // Handle multiple supporting documents (files/images)
        $supportingDocUrls = [];
        if ($request->hasFile('supporting_doc_url')) {
            // Ensure directory exists
            $uploadPath = public_path(self::STORAGE_PATH);
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            foreach ($request->file('supporting_doc_url') as $file) {
                $filename = uniqid('maintenance_doc_') . '.' . $file->getClientOriginalExtension();
                $file->move($uploadPath, $filename);
                $supportingDocUrls[] = $filename;
            }
        }

        $data = $request->validated();

        // Store as JSON array
        $data['supporting_doc_url'] = json_encode($supportingDocUrls);
        $data['entry_date'] = date('Y-m-d', strtotime($data['entry_date']));
        $data['notify_by_email'] = in_array('email', $data['notifications']);
        $data['notify_by_phone'] = in_array('phone', $data['notifications']);
        $data['notify_in_app'] = in_array('inapp', $data['notifications']);
        unset($data['notifications']);
        $data['status'] = 'pending';
        $data['created_by'] = auth()->user()->id;
        $data['modified_by'] = auth()->user()->id;

        $maintenance = Maintenance::create($data);
        $this->updateService->maintenanceCreated($maintenance);

        if ($maintenance) {
            $request->session()->flash('success', 'Maintenance entry added successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, Maintenance $external): View
    {
        $actions = $this->actionService->getActionsBasedOnWorkflowStage($external->workflow_id);

        return view('estate.estate.maintenance.external-maintenance.show', [
            'maintenance' => $external,
            'actions' => $actions,
            'forwarders' => $external->workflow->handlerUsers(),
            'hasNextStage' => $this->workflowService->hasNextStage($external->workflow),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Request $request, Maintenance $external)
    {
        if (auth()->user()->cannot('update.external-maintenance')) {
            abort(403);
        }

        return view('estate.estate.maintenance.external-maintenance.edit', [
            'maintenance' => $external->load([
                'department',
                'workType',
                'checkedBy',
            ]),
            'departments' => Department::where('is_active', true)->get(),
            'workTypes' => WorkType::where('is_active', true)->get(),
            'users' => User::all(),
        ]);
    }

    public function update(MaintenanceUpdateRequest $request, Maintenance $external): Response|ResponseFactory
    {
        if (auth()->user()->cannot('update.external-maintenance')) {
            abort(403);
        }

        $workflow = $this->workflowService->getWorkflowById($external->workflow_id);

        if (
            $workflow->attachment_required &&
            (empty($external->supporting_doc_url) || $external->supporting_doc_url === "[]") &&
            !$request->hasFile('supporting_doc_url')
        ) {
            return $this->error(
                'Validation Error',
                422,
                ['supporting_doc_url' => ['Supporting documents are required.']],
            );
        }

        // Start with existing files
        $existingFiles = $external->supporting_doc_url ? json_decode($external->supporting_doc_url, true) : [];

        $newFiles = [];
        if ($request->hasFile('supporting_doc_url')) {
            // Ensure directory exists
            $uploadPath = public_path(self::STORAGE_PATH);
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            foreach ($request->file('supporting_doc_url') as $file) {
                $filename = uniqid('maintenance_doc_') . '.' . $file->getClientOriginalExtension();
                $file->move($uploadPath, $filename);
                $newFiles[] = $filename;
            }
        }

        $data = $request->validated();

        // Merge old and new files
        $allFiles = array_merge($existingFiles, $newFiles);
        $data['supporting_doc_url'] = json_encode($allFiles);
        $data['entry_date'] = date('Y-m-d', strtotime($data['entry_date']));
        $data['notify_by_email'] = in_array('email', $data['notifications']);
        $data['notify_by_phone'] = in_array('phone', $data['notifications']);
        $data['notify_in_app'] = in_array('inapp', $data['notifications']);
        unset($data['notifications']);
        $data['modified_by'] = auth()->user()->id;

        $updatedMaintenance = $this->service->update($external, $data);
        $this->updateService->maintenanceEdited($updatedMaintenance);

        if ($updatedMaintenance) {
            $request->session()->flash('success', 'Maintenance updated successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }


    public function destroy(Request $request, Maintenance $external): RedirectResponse
    {
        if (auth()->user()->cannot('delete.external-maintenance')) {
            abort(403);
        }

        $deleted = $this->service->delete($external);
        if ($deleted) {
            $request->session()->flash('success', 'External maintenance entry deleted successfully');
        } else {
            $request->session()->flash('error', 'Something went wrong!');
        }
        return redirect()->route('estate.maintenance.external.index');
    }

    /**
     * Handle AJAX file upload for supporting documents
     */
    public function uploadSupportingDocs(Request $request)
    {
        if ($request->hasFile('file')) {
            $file = $request->file('file');

            // Validate file
            $request->validate([
                'file' => 'required|file|mimes:png,jpg,jpeg,pdf|max:2048'
            ]);

            $filename = uniqid('maintenance_doc_') . '.' . $file->getClientOriginalExtension();

            // Ensure directory exists
            $uploadPath = public_path(self::STORAGE_PATH);
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $file->move($uploadPath, $filename);

            return response()->json([
                'success' => true,
                'filename' => $filename,
                'url' => asset(self::STORAGE_PATH . $filename)
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'No file uploaded'
        ], 400);
    }

    /**
     * AJAX: Delete a supporting document from a maintenance entry
     */
    public function deleteSupportingDoc(Request $request)
    {
        $request->validate([
            'maintenance_id' => 'required|integer|exists:maintenances,id',
            'filename' => 'required|string'
        ]);

        $maintenance = Maintenance::findOrFail($request->maintenance_id);
        $docs = is_array($maintenance->supporting_doc_url)
            ? $maintenance->supporting_doc_url
            : (is_string($maintenance->supporting_doc_url) ? json_decode($maintenance->supporting_doc_url, true) : []);

        $filename = $request->filename;
        $newDocs = array_filter($docs, fn($doc) => $doc !== $filename);

        // Remove file from storage
        $filePath = public_path(self::STORAGE_PATH . $filename);
        if (file_exists($filePath)) {
            @unlink($filePath);
        }

        // Update DB
        $maintenance->supporting_doc_url = json_encode(array_values($newDocs));
        $maintenance->save();

        return response()->json(['success' => true]);
    }

    public function addUpdate(EstateUpdateStoreRequest $request, Maintenance $external): Response|ResponseFactory
    {
        if ($external->workflow->attachment_required && !$request->hasFile('supporting_doc_url')) {
            return $this->error(
                'Validation Error',
                422,
                ['supporting_doc_url' => ['Supporting documents are required.']],
            );
        }
        // Handle multiple supporting documents (files/images)
        $supportingDocUrls = [];
        if ($request->hasFile('supporting_doc_url')) {
            // Ensure directory exists
            $uploadPath = public_path(self::STORAGE_PATH);
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            foreach ($request->file('supporting_doc_url') as $file) {
                $filename = uniqid('maintenance_update_doc_') . '.' . $file->getClientOriginalExtension();
                $file->move($uploadPath, $filename);
                $supportingDocUrls[] = $filename;
            }
        }

        $data = $request->validated();
        $updatedMaintenance = $this->service->applyActionOnMaintenance($external, $data['action_id']);

        $data['supporting_doc_url'] = json_encode($supportingDocUrls);
        $update = $this->updateService->applyUpdateToMaintenance($updatedMaintenance, $data);

        if ($update) {
            $request->session()->flash('success', 'Update submitted successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }
}

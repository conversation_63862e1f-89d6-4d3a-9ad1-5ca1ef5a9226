<?php

namespace App\Http\Controllers\Estate\ServiceProviders;

use App\Http\Controllers\Controller;
use App\Http\Requests\Estate\ServiceProviders\AgentStoreRequest;
use App\Http\Requests\Estate\ServiceProviders\AgentUpdateRequest;
use App\Http\Resources\Estate\ServiceProviders\AgentCollection;
use App\Http\Resources\Estate\ServiceProviders\AgentResource;
use App\Models\Estate\ServiceProviders\Agent;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AgentController extends Controller
{
    public function index(Request $request): AgentCollection
    {
        $agents = Agent::all();

        return new AgentCollection($agents);
    }

    public function store(AgentStoreRequest $request): AgentResource
    {
        $agent = Agent::create($request->validated());

        return new AgentResource($agent);
    }

    public function show(Request $request, Agent $agent): AgentResource
    {
        return new AgentResource($agent);
    }

    public function update(AgentUpdateRequest $request, Agent $agent): AgentResource
    {
        $agent->update($request->validated());

        return new AgentResource($agent);
    }

    public function destroy(Request $request, Agent $agent): Response
    {
        $agent->delete();

        return response()->noContent();
    }
}

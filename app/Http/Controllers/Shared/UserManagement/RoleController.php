<?php

namespace App\Http\Controllers\Shared\UserManagement;

use App\Http\Controllers\Controller;
use App\Http\Requests\Shared\UserManagement\RoleStoreRequest;
use App\Http\Requests\Shared\UserManagement\RoleUpdateRequest;
use App\Http\Resources\RoleResource;
use App\Models\Shared\UserManagement\Role;
use App\Models\User;
use App\Services\Shared\UserManagement\PermissionService;
use App\Services\Shared\UserManagement\RoleService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Contracts\Routing\ResponseFactory;
use Symfony\Component\HttpFoundation\Response;

class RoleController extends Controller
{
    public function __construct(
        protected RoleService $service,
        protected PermissionService $permissionService,
    ) {
        $this->middleware('can:read.role');
    }

    public function index(Request $request): View
    {
        return view('shared.user-management.role.index', [
            'roles' => $this->service->getAllRoles(),
            'permiisionsBySubModules' => $this->permissionService->getPermissionsBySubModule(),
        ]);
    }

    public function store(RoleStoreRequest $request): Response|ResponseFactory
    {
        if (auth()->user()->cannot('create.role')) {
            abort(403);
        }

        $role = Role::create([
            ...$request->validated(),
            'guard_name' => 'web',
            'created_by' => auth()->user()->id,
            'modified_by' => auth()->user()->id,
        ]);

        $this->service->syncPermissions($role, $request->permissions);

        if ($role) {
            $request->session()->flash('success', 'Role added successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function show(Request $request, Role $role): View
    {
        return view('shared.user-management.role.show', [
            'role' => $role->load(['users']),
        ]);
    }

    public function edit(Request $request, Role $role): View
    {
        if (auth()->user()->cannot('update.role')) {
            abort(403);
        }

        return view('shared.user-management.role.edit', [
            'role' => $role,
            'subModules' => $this->permissionService->getPermissionsBySubModule(),
        ]);
    }

    public function update(RoleUpdateRequest $request, Role $role): Response|ResponseFactory
    {
        if (auth()->user()->cannot('update.role')) {
            abort(403);
        }

        $this->service->syncPermissions($role, $request->permissions);

        $updated = $role->update([
            ...$request->validated(),
            'guard' => 'web',
            'modified_by' => auth()->user()->id,
        ]);

        if ($updated) {
            $request->session()->flash('success', 'Role updated successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }

    public function destroy(Request $request, Role $role): RedirectResponse
    {
        if (auth()->user()->cannot('delete.role')) {
            abort(403);
        }

        if ($role->users()->count() > 0) {
            $request->session()->flash('error', 'Role cannot be deleted as it is assigned to users');
            return redirect()->route('roles.index');
        }

        $role->deleteOrFail();
        $request->session()->flash('success', 'Role removed successfully');
        return redirect()->route('roles.index');
    }

    public function unassign(Request $request, Role $role, User $user): RedirectResponse
    {
        if (auth()->user()->cannot('update.role')) {
            abort(403);
        }

        $affectedRows = $this->service->unAssignRoleFromUsers($role, [$user->id]);
        if ($affectedRows > 0) {
            $request->session()->flash('success', 'User unassigned from role successfully');
        } else {
            $request->session()->flash('error', 'Something went wrong!');
        }
        return redirect()->back();
    }

    public function unassignMany(Request $request, Role $role): RoleResource
    {
        if (auth()->user()->cannot('update.role')) {
            abort(403);
        }

        $request->validate([
            'users' => ['required', 'array'],
            'users.*' => ['required', 'string', 'exists:users,id'],
        ]);

        $this->service->unAssignRoleFromUsers($role, $request['users']);
        return new RoleResource($role);
    }
}

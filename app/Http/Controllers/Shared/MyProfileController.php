<?php

namespace App\Http\Controllers\Shared;

use App\Http\Controllers\Controller;
use App\Http\Requests\Shared\PasswordUpdateRequest;
use App\Models\User;
use App\Services\Shared\UserManagement\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Contracts\Routing\ResponseFactory;
use Symfony\Component\HttpFoundation\Response;

class MyProfileController extends Controller
{
    public function __construct(protected UserService $service)
    {
        $this->middleware('can:read.profile');
    }

    public function index(Request $request): View
    {
        return view('shared.my.my-profile');
    }

    public function update(PasswordUpdateRequest $request): Response|ResponseFactory
    {
        if (auth()->user()->cannot('update.password')) {
            abort(403);
        }

        $data = $request->validated();
        $data['password'] = bcrypt($data['password']);

        $user = auth()->user();
        $updated = $this->service->update($user, $data);
        if ($updated) {
            $request->session()->flash('success', 'Password updated successfully');
            return $this->success();
        } else {
            $request->session()->flash('error', 'Something went wrong!');
            return $this->error('Something went wrong!');
        }
    }
}

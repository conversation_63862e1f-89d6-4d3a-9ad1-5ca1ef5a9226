<?php

namespace App\Http\Requests\Shared\UserManagement;

use Illuminate\Foundation\Http\FormRequest;

class UserUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'avatar' => ['nullable', 'mimes:png,jpg,jpeg', 'max:2048'],
            'avatar_remove' => ['nullable', 'in:0,1'],
            'fullname' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email,' . $this->user->id],
            'phone_no' => ['nullable', 'string', 'max:255', 'unique:users,phone_no,' . $this->user->id],
            'gender' => ['required', 'string', 'in:male,female'],
            'marital_status' => ['required', 'string', 'in:single,married,widowed,divorced'],
            'password' => ['nullable', 'string', 'min:8', 'confirmed'],
            'role_id' => ['nullable', 'exists:roles,id'],
            'rank_id' => ['nullable', 'exists:user_ranks,id'],
            'is_active' => ['nullable', 'in:0,1'],
        ];
    }

    public function messages(): array
    {
        return [
            'avatar.required' => 'A profile picture is required.',
        ];
    }
}

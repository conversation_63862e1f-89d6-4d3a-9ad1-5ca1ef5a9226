<?php

namespace App\Http\Resources\Estate\ServiceProviders;

use App\Http\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AgentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'cooperate_name' => $this->cooperate_name,
            'cooperate_address' => $this->cooperate_address,
            'cooperate_telephone' => $this->cooperate_telephone,
            'cooperate_email' => $this->cooperate_email,
            'cooperate_registration_no' => $this->cooperate_registration_no,
            'cooperate_nature_of_business' => $this->cooperate_nature_of_business,
            'company_logo' => $this->company_logo,
            'fullname' => $this->fullname,
            'gender' => $this->gender,
            'religion' => $this->religion,
            'marital_status' => $this->marital_status,
            'personal_email' => $this->personal_email,
            'residential_address' => $this->residential_address,
            'city_id' => $this->city_id,
            'means_of_identification' => $this->means_of_identification,
            'date_of_birth' => $this->date_of_birth,
            'mobile_number' => $this->mobile_number,
            'tenant_category_id' => $this->tenant_category_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Relationships
            'city' => $this->whenLoaded('city'),
            'tenant_category' => $this->whenLoaded('tenantCategory'),
            'services' => AgentServiceResource::collection($this->whenLoaded('services')),
            'active_services' => AgentServiceResource::collection($this->whenLoaded('activeServices')),
            'external_maintenances' => $this->whenLoaded('externalMaintenances'),
            'created_by' => new UserResource($this->whenLoaded('createdBy')),
            'modified_by' => new UserResource($this->whenLoaded('modifiedBy')),

            // Computed attributes
            'display_name' => $this->display_name,
            'full_name' => $this->full_name,
            'primary_email' => $this->primary_email,
            'is_corporate' => $this->isCorporate(),
            'services_count' => $this->whenCounted('services'),
            'active_services_count' => $this->whenCounted('activeServices'),
        ];
    }
}

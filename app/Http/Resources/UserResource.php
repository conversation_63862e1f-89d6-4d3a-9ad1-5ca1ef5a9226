<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'identifier' => $this->identifier,
            'fullname' => $this->fullname,
            'email' => $this->email,
            'phone_no' => $this->phone_no,
            'profile_picture' => $this->profile_picture,
            'is_active' => $this->is_active,
            'user_type' => $this->user_type,
            'email_verified_at' => $this->email_verified_at,
            'rank_id' => $this->rank_id,
            'first_access' => $this->first_access,
            'last_access' => $this->last_access,
            'rank' => new UserRankResource($this->rank),
        ];
    }
}

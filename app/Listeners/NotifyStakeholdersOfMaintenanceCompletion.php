<?php

namespace App\Listeners;

use App\Events\ExternalMaintenanceCompleted;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class NotifyStakeholdersOfMaintenanceCompletion implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(
        protected NotificationService $notificationService
    ) {
    }

    /**
     * Handle the event.
     */
    public function handle(ExternalMaintenanceCompleted $event): void
    {
        try {
            $maintenance = $event->externalMaintenance;
            $agent = $event->agent;
            $completedBy = $event->completedBy;
            $completionNotes = $event->completionNotes;

            // Notify stakeholders about completion
            $this->notifyStakeholders($maintenance, $agent, $completedBy, $completionNotes);

            // Notify the agent about successful completion
            $this->notifyAgent($maintenance, $agent, $completedBy);

            Log::info('Maintenance completion notifications sent', [
                'maintenance_id' => $maintenance->id,
                'agent_id' => $agent->id,
                'completed_by' => $completedBy?->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send maintenance completion notifications', [
                'maintenance_id' => $event->externalMaintenance->id,
                'agent_id' => $event->agent->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Notify relevant stakeholders about completion.
     */
    private function notifyStakeholders($maintenance, $agent, $completedBy, $completionNotes): void
    {
        $notification = [
            'title' => 'External Maintenance Completed',
            'message' => "Maintenance task #{$maintenance->id} at {$maintenance->location} has been completed by {$agent->display_name}.",
            'action_url' => route('external-maintenance.show', $maintenance->id),
            'action_text' => 'View Details',
            'data' => [
                'maintenance_id' => $maintenance->id,
                'agent_id' => $agent->id,
                'agent_name' => $agent->display_name,
                'location' => $maintenance->location,
                'work_type' => $maintenance->workType?->name,
                'completion_date' => now()->format('Y-m-d H:i:s'),
                'completed_by' => $completedBy?->id,
                'completion_notes' => $completionNotes,
                'cost_implication' => $maintenance->cost_implication,
                'type' => 'maintenance_completion'
            ],
        ];

        // Notify department heads, estate managers, and supervisors
        $this->notificationService->sendToRoles(
            ['estate_manager', 'department_head', 'maintenance_supervisor', 'admin'],
            $notification['title'],
            $notification['message'],
            $notification
        );

        // Notify the person who checked the maintenance
        if ($maintenance->checked_by) {
            $this->notificationService->sendToUser(
                $maintenance->checked_by,
                $notification['title'],
                $notification['message'],
                $notification
            );
        }

        // Notify the person who created the maintenance request
        if ($maintenance->created_by) {
            $this->notificationService->sendToUser(
                $maintenance->created_by,
                $notification['title'],
                $notification['message'],
                $notification
            );
        }
    }

    /**
     * Notify the agent about successful completion.
     */
    private function notifyAgent($maintenance, $agent, $completedBy): void
    {
        $notification = [
            'title' => 'Maintenance Task Completed',
            'message' => "Thank you for completing maintenance task #{$maintenance->id}. The completion has been recorded.",
            'action_url' => route('external-maintenance.show', $maintenance->id),
            'action_text' => 'View Details',
            'data' => [
                'maintenance_id' => $maintenance->id,
                'agent_id' => $agent->id,
                'location' => $maintenance->location,
                'completion_date' => now()->format('Y-m-d H:i:s'),
                'completed_by' => $completedBy?->id,
                'type' => 'maintenance_completion_agent'
            ],
        ];

        // Send to agent if they have a user account
        if ($agent->user_id) {
            $this->notificationService->sendToUser(
                $agent->user_id,
                $notification['title'],
                $notification['message'],
                $notification
            );
        }
    }
}

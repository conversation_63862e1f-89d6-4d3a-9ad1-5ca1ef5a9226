<?php

namespace App\Listeners;

use App\Events\MaintenanceEvent;
use App\Interfaces\MaintenanceNotificationInterface;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class MaintenanceEventNotification
{
        /** @var MaintenanceNotificationInterface[] */
    private $senders = [];

    /**
     * Auto-discover all notificatiom sender classes in the maintenance directory.
     *
     * @return void
     */
    public function __construct()
    {
        $sendersDir = __DIR__ . '/Maintenance';
        foreach (glob("$sendersDir/*.php") as $fileName) {
            require_once $fileName;
            $className = 'App\\Listeners\\Maintenance\\' . basename($fileName, '.php');
            if (class_exists($className)) {
                $sender = app($className);
                if ($sender instanceof MaintenanceNotificationInterface) {
                    $this->senders[] = $sender;
                }
            }
        }
    }

    public function handle(MaintenanceEvent $event): void
    {
        foreach ($this->senders as $sender) {
            if ($sender->matches($event)) {
                $sender->send($event);
            }
        }
    }
}

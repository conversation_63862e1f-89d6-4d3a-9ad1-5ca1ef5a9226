<?php

namespace App\Listeners;

use App\Events\AgentRegistered;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendAgentWelcomeNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(
        protected NotificationService $notificationService
    ) {
    }

    /**
     * Handle the event.
     */
    public function handle(AgentRegistered $event): void
    {
        try {
            $agent = $event->agent;
            $displayName = $agent->display_name;

            $notification = [
                'title' => 'Welcome to Our Service Provider Network',
                'message' => "Dear {$displayName}, welcome to our service provider network. Your registration has been successfully completed.",
                'action_url' => route('agents.show', $agent->id),
                'action_text' => 'View Profile',
                'data' => [
                    'agent_id' => $agent->id,
                    'registration_date' => $agent->created_at->format('Y-m-d H:i:s'),
                    'type' => 'agent_welcome'
                ],
            ];

            // Send notification to agent if they have a user account
            if ($agent->user_id) {
                $this->notificationService->sendToUser(
                    $agent->user_id,
                    $notification['title'],
                    $notification['message'],
                    $notification
                );
            }

            Log::info('Agent welcome notification sent', [
                'agent_id' => $agent->id,
                'agent_name' => $displayName
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send agent welcome notification', [
                'agent_id' => $event->agent->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}

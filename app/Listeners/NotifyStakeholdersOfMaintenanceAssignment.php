<?php

namespace App\Listeners;

use App\Events\ExternalMaintenanceAssigned;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class NotifyStakeholdersOfMaintenanceAssignment implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(
        protected NotificationService $notificationService
    ) {
    }

    /**
     * Handle the event.
     */
    public function handle(ExternalMaintenanceAssigned $event): void
    {
        try {
            $maintenance = $event->externalMaintenance;
            $agent = $event->agent;
            $assignedBy = $event->assignedBy;

            // Notify the assigned agent
            $this->notifyAgent($maintenance, $agent, $assignedBy);

            // Notify relevant stakeholders
            $this->notifyStakeholders($maintenance, $agent, $assignedBy);

            Log::info('Maintenance assignment notifications sent', [
                'maintenance_id' => $maintenance->id,
                'agent_id' => $agent->id,
                'assigned_by' => $assignedBy?->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send maintenance assignment notifications', [
                'maintenance_id' => $event->externalMaintenance->id,
                'agent_id' => $event->agent->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Notify the assigned agent.
     */
    private function notifyAgent($maintenance, $agent, $assignedBy): void
    {
        $notification = [
            'title' => 'New Maintenance Assignment',
            'message' => "You have been assigned a new maintenance task (#{$maintenance->id}) at {$maintenance->location}.",
            'action_url' => route('external-maintenance.show', $maintenance->id),
            'action_text' => 'View Details',
            'data' => [
                'maintenance_id' => $maintenance->id,
                'agent_id' => $agent->id,
                'location' => $maintenance->location,
                'work_type' => $maintenance->workType?->name,
                'description' => $maintenance->description,
                'cost_implication' => $maintenance->cost_implication,
                'assigned_by' => $assignedBy?->id,
                'assignment_date' => now()->format('Y-m-d H:i:s'),
                'type' => 'maintenance_assignment'
            ],
        ];

        // Send to agent if they have a user account
        if ($agent->user_id) {
            $this->notificationService->sendToUser(
                $agent->user_id,
                $notification['title'],
                $notification['message'],
                $notification
            );
        }
    }

    /**
     * Notify relevant stakeholders.
     */
    private function notifyStakeholders($maintenance, $agent, $assignedBy): void
    {
        $notification = [
            'title' => 'External Maintenance Assigned',
            'message' => "Maintenance task #{$maintenance->id} has been assigned to {$agent->display_name}.",
            'action_url' => route('external-maintenance.show', $maintenance->id),
            'action_text' => 'View Details',
            'data' => [
                'maintenance_id' => $maintenance->id,
                'agent_id' => $agent->id,
                'agent_name' => $agent->display_name,
                'location' => $maintenance->location,
                'work_type' => $maintenance->workType?->name,
                'assigned_by' => $assignedBy?->id,
                'assignment_date' => now()->format('Y-m-d H:i:s'),
                'type' => 'maintenance_assignment_stakeholder'
            ],
        ];

        // Notify department heads, estate managers, and supervisors
        $this->notificationService->sendToRoles(
            ['estate_manager', 'department_head', 'maintenance_supervisor'],
            $notification['title'],
            $notification['message'],
            $notification
        );

        // Notify the person who checked the maintenance if different from assignedBy
        if ($maintenance->checked_by && $maintenance->checked_by !== $assignedBy?->id) {
            $this->notificationService->sendToUser(
                $maintenance->checked_by,
                $notification['title'],
                $notification['message'],
                $notification
            );
        }
    }
}

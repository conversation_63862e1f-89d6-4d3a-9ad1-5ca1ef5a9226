<?php

namespace App\Listeners;

use App\Events\UserRegistered;
use App\Events\UserRegisteredSecure;
use App\Mails\NewUserCredentialsMail;
use App\Notifications\WelcomeUserNotification;
use App\Services\MailService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendNewUserCredentialsMail
{
    /**
     * Create the event listener.
     */
    public function __construct(protected MailService $mailService)
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegisteredSecure $event): void
    {
        $mail = new NewUserCredentialsMail(
            $event->user,
            $event->password
        );
        $this->mailService->sendCustomMail($event->user, $mail);
    }
}

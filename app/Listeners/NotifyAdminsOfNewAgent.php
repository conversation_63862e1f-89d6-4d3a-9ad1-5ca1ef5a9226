<?php

namespace App\Listeners;

use App\Events\AgentRegistered;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class NotifyAdminsOfNewAgent implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(
        protected NotificationService $notificationService
    ) {
    }

    /**
     * Handle the event.
     */
    public function handle(AgentRegistered $event): void
    {
        try {
            $agent = $event->agent;
            $registeredBy = $event->registeredBy;
            $displayName = $agent->display_name;

            $notification = [
                'title' => 'New Service Provider Registered',
                'message' => "A new service provider '{$displayName}' has been registered by {$registeredBy?->fullname}.",
                'action_url' => route('agents.show', $agent->id),
                'action_text' => 'Review Agent',
                'data' => [
                    'agent_id' => $agent->id,
                    'agent_name' => $displayName,
                    'agent_type' => $agent->isCorporate() ? 'Corporate' : 'Individual',
                    'registered_by' => $registeredBy?->id,
                    'registration_date' => $agent->created_at->format('Y-m-d H:i:s'),
                    'type' => 'new_agent_registration'
                ],
            ];

            // Send notification to admins and relevant roles
            $this->notificationService->sendToRoles(
                ['admin', 'estate_manager', 'service_coordinator'],
                $notification['title'],
                $notification['message'],
                $notification
            );

            Log::info('Admin notification sent for new agent registration', [
                'agent_id' => $agent->id,
                'agent_name' => $displayName,
                'registered_by' => $registeredBy?->id
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send admin notification for new agent', [
                'agent_id' => $event->agent->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}

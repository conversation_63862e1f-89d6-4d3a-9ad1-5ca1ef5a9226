<?php

declare(strict_types=1);

namespace App\Listeners\Maintenance;

use App\Events\MaintenanceEvent;
use App\Interfaces\MaintenanceNotificationInterface;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class ForwardToNotificationSender implements MaintenanceNotificationInterface
{
    public function __construct(protected NotificationService $notificationService)
    {
    }

    public function matches(MaintenanceEvent $event): bool
    {
        return is_null($event->update->forward_to) !== true;
    }

    public function send(MaintenanceEvent $event): void
    {
        $link = route(
            $event->maintenance->type === 'internal'
                ? 'estate.maintenance.internal.show'
                : 'estate.maintenance.external.show',
            $event->maintenance->id
        );

        $data = [
            'maintenance_id' => $event->maintenance->id,
            'update_id' => $event->update->id,
            'action_id' => $event->action->id,
        ];

        $notification = [
            'title' => 'Maintenance Entry Forwarded To You',
            'message' => "A maintenance entry with reference #{$event->maintenance->id} has been forwarded to you by
                {$event->update->createdBy->fullname}",
            'action_url' => $link,
            'action_text' => 'View Details',
            'data' => $data,
        ];

        $this->notificationService->sendToUser(
            $event->update->forward_to,
            $notification['title'],
            $notification['message'],
            $notification
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Listeners\Maintenance;

use App\Events\MaintenanceEvent;
use App\Interfaces\MaintenanceNotificationInterface;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotifsNotificationSender implements MaintenanceNotificationInterface
{
    public function __construct(protected NotificationService $notificationService)
    {
    }

    public function matches(MaintenanceEvent $event): bool
    {
        return true;
    }

    public function send(MaintenanceEvent $event): void
    {
        $notifs = $event->maintenance->workflow->observers()->pluck('id')->toArray();

        $link = route(
            $event->maintenance->type === 'internal'
                ? 'estate.maintenance.internal.show'
                : 'estate.maintenance.external.show',
            $event->maintenance->id
        );
        $data = [
            'maintenance_id' => $event->maintenance->id,
            'update_id' => $event->update->id,
            'action_id' => $event->action->id,
        ];

        $notification = match ($event->action->status) {
            'created' => [
                'title' => 'New Maintenance Entry',
                'message' => "A new maintenance entry has been created by {$event->maintenance->createdBy->fullname}",
                'action_url' => $link,
                'action_text' => 'View Details',
                'data' => $data,
            ],
            'edited' => [
                'title' => 'Maintenance Entry Edited',
                'message' => "A maintenance entry with reference #{$event->maintenance->id} has been updated by
                    {$event->maintenance->modifiedBy->fullname}.",
                'action_url' => $link,
                'action_text' => 'View Details',
                'data' => $data,
            ],
            default => [
                'title' => 'New action performed on Maintenance Entry',
                'message' => "A maintenance entry with reference #{$event->maintenance->id} has been acted upon by
                    {$event->update->createdBy->fullname}.",
                'action_url' => $link,
                'action_text' => 'View Details',
                'data' => $data,
            ],
        };
        $this->notificationService->sendToRanks(
            $notifs,
            $notification['title'],
            $notification['message'],
            $notification
        );
    }
}

<?php

namespace App\Repositories\Shared;

use App\Models\Shared\Workflow;
use Illuminate\Database\Eloquent\Collection;

class WorkflowRepository
{
    public function __construct(protected Workflow $modal)
    {
    }

    public function create(array $data): Workflow
    {
        return $this->modal->create($data);
    }

    public function update(Workflow $workflow, array $data): Workflow
    {
        $workflow->update($data);

        return $workflow;
    }

    public function delete(Workflow $workflow): bool
    {
        return $workflow->delete();
    }

    public function getAllWorkflows(): Collection
    {
        return $this->modal->all();
    }

    public function getActiveWorkflows(): Collection
    {
        return $this->modal->where('is_active', 1)->get();
    }

    public function getWorkflowById(int $id): Workflow
    {
        return $this->modal->find($id);
    }

    public function getWorkflowByMenuAndStage(int $menuId, int $stageNumber): Workflow|null
    {
        return $this->modal->where('menu_id', $menuId)
            ->where('stage_number', $stageNumber)
            ->first();
    }

    public function hasNextStage(Workflow $workflow): bool
    {
        return $this->modal->where('menu_id', $workflow->menu_id)
            ->where('stage_number', '>', $workflow->stage_number)
            ->exists();
    }
}

<?php

namespace App\Repositories\Estate\ServiceProviders;

use App\Models\Estate\ServiceProviders\Agent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class AgentRepository
{
    public function __construct(protected Agent $model)
    {
    }

    /**
     * Create a new agent.
     */
    public function create(array $data): Agent
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing agent.
     */
    public function update(Agent $agent, array $data): Agent
    {
        $agent->update($data);
        return $agent;
    }

    /**
     * Delete an agent.
     */
    public function delete(Agent $agent): bool
    {
        return $agent->delete();
    }

    /**
     * Get all agents with relationships.
     */
    public function getAllAgents(): Collection
    {
        return $this->model->with([
            'services',
            'activeServices',
            'externalMaintenances',
            'city',
            'tenantCategory',
            'createdBy',
            'modifiedBy'
        ])->get();
    }

    /**
     * Get paginated agents.
     */
    public function getPaginatedAgents(int $perPage = 15): LengthAwarePaginator
    {
        return $this->model->with([
            'services',
            'activeServices',
            'city',
            'tenantCategory',
            'createdBy'
        ])->paginate($perPage);
    }

    /**
     * Get agent by ID with relationships.
     */
    public function getAgentById(int $id): ?Agent
    {
        return $this->model->with([
            'services',
            'activeServices',
            'externalMaintenances',
            'city',
            'tenantCategory',
            'createdBy',
            'modifiedBy'
        ])->find($id);
    }

    /**
     * Get corporate agents.
     */
    public function getCorporateAgents(): Collection
    {
        return $this->model->corporate()
            ->with(['services', 'city', 'tenantCategory'])
            ->get();
    }

    /**
     * Get individual agents.
     */
    public function getIndividualAgents(): Collection
    {
        return $this->model->individual()
            ->with(['services', 'city', 'tenantCategory'])
            ->get();
    }

    /**
     * Search agents by name or email.
     */
    public function searchAgents(string $query): Collection
    {
        return $this->model->where(function ($q) use ($query) {
            $q->where('cooperate_name', 'like', "%{$query}%")
              ->orWhere('fullname', 'like', "%{$query}%")
              ->orWhere('cooperate_email', 'like', "%{$query}%")
              ->orWhere('personal_email', 'like', "%{$query}%");
        })->with(['services', 'city', 'tenantCategory'])->get();
    }

    /**
     * Get agents by city.
     */
    public function getAgentsByCity(int $cityId): Collection
    {
        return $this->model->where('city_id', $cityId)
            ->with(['services', 'city', 'tenantCategory'])
            ->get();
    }

    /**
     * Get agents by tenant category.
     */
    public function getAgentsByTenantCategory(int $categoryId): Collection
    {
        return $this->model->where('tenant_category_id', $categoryId)
            ->with(['services', 'city', 'tenantCategory'])
            ->get();
    }

    /**
     * Get agents with active services.
     */
    public function getAgentsWithActiveServices(): Collection
    {
        return $this->model->whereHas('activeServices')
            ->with(['activeServices', 'city', 'tenantCategory'])
            ->get();
    }

    /**
     * Get agents by service category.
     */
    public function getAgentsByServiceCategory(string $category): Collection
    {
        return $this->model->whereHas('activeServices', function ($query) use ($category) {
            $query->where('service_category', $category);
        })->with(['activeServices', 'city', 'tenantCategory'])->get();
    }

    /**
     * Get agents by service type.
     */
    public function getAgentsByServiceType(string $type): Collection
    {
        return $this->model->whereHas('activeServices', function ($query) use ($type) {
            $query->where('service_type', $type);
        })->with(['activeServices', 'city', 'tenantCategory'])->get();
    }

    /**
     * Get agents with services in cost range.
     */
    public function getAgentsWithServicesInCostRange(float $minCost, float $maxCost): Collection
    {
        return $this->model->whereHas('activeServices', function ($query) use ($minCost, $maxCost) {
            $query->where('minimum_cost', '<=', $maxCost)
                  ->where('maximum_cost', '>=', $minCost);
        })->with(['activeServices', 'city', 'tenantCategory'])->get();
    }

    /**
     * Get agent statistics.
     */
    public function getAgentStatistics(): array
    {
        return [
            'total_agents' => $this->model->count(),
            'corporate_agents' => $this->model->corporate()->count(),
            'individual_agents' => $this->model->individual()->count(),
            'agents_with_services' => $this->model->whereHas('services')->count(),
            'agents_with_active_services' => $this->model->whereHas('activeServices')->count(),
        ];
    }
}

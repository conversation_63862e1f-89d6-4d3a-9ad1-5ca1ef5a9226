<?php

namespace App\Events;

use App\Models\Estate\Estate\ExternalMaintenance;
use App\Models\Estate\ServiceProviders\Agent;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ExternalMaintenanceAssigned
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public ExternalMaintenance $externalMaintenance,
        public Agent $agent,
        public ?User $assignedBy = null
    ) {
        $this->assignedBy = $assignedBy ?: auth()->user();
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('external-maintenance'),
        ];
    }
}

<?php

namespace App\Events;

use App\Models\Estate\Estate\EstateAction;
use App\Models\Estate\Estate\EstateUpdate;
use App\Models\Estate\Estate\Maintenance;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class MaintenanceEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public $user;


    /**
     * Create a new event instance.
     */
    public function __construct(
        public Maintenance $maintenance,
        public EstateAction $action,
        public EstateUpdate $update
    ) {
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}

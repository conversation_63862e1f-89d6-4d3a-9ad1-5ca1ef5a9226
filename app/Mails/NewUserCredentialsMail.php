<?php

declare(strict_types=1);

namespace App\Mails;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewUserCredentialsMail extends Mailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        protected User $user,
        protected string $password
    ) {
        $this->afterCommit();
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): Mailable
    {
        return $this->from(config('mail.from.address'), config('mail.from.address'))
            ->subject(config('app.name') . " - New Account Credentials")
            ->view('mails.new-user-credentials', [
                'user' => $this->user,
                'password' => $this->password,
            ]);
    }
}

<?php

declare(strict_types=1);

namespace App\Interfaces;

use App\Events\MaintenanceEvent;

/**
 * Interface for a class that sends payment notifications.
 */
interface MaintenanceNotificationInterface
{
    /**
     * Determines if this notification sender should handle the given event.
     */
    public function matches(MaintenanceEvent $event): bool;

    /**
     * Send the notification.
     */
    public function send(MaintenanceEvent $event): void;
}
